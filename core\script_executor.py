#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本执行器
管理Python脚本的执行过程
"""

import sys
import subprocess
import threading
import time
import os
from pathlib import Path
from typing import Dict, Optional, Callable, Any
from utils.logger import get_logger


class ScriptExecutor:
    """脚本执行器"""
    
    def __init__(self, python_executable: str = None):
        self.logger = get_logger(__name__)
        self.python_executable = python_executable or sys.executable
        self.running_processes = {}  # 存储正在运行的进程
        
    def execute_script(self, script_path: str, 
                      config: Dict[str, Any] = None,
                      output_callback: Callable[[str, str], None] = None,
                      finished_callback: Callable[[Dict[str, Any]], None] = None) -> str:
        """
        执行Python脚本
        
        Args:
            script_path: 脚本文件路径
            config: 执行配置
            output_callback: 输出回调函数 (line, type)
            finished_callback: 完成回调函数
            
        Returns:
            str: 执行ID
        """
        try:
            script_path = Path(script_path)
            if not script_path.exists():
                raise FileNotFoundError(f"脚本文件不存在: {script_path}")
                
            # 生成执行ID
            execution_id = f"exec_{int(time.time() * 1000)}"
            
            # 默认配置
            if config is None:
                config = {}
                
            self.logger.info(f"开始执行脚本: {script_path} (ID: {execution_id})")
            
            # 创建执行线程
            thread = threading.Thread(
                target=self._execute_script_thread,
                args=(execution_id, script_path, config, output_callback, finished_callback),
                daemon=True
            )
            thread.start()
            
            return execution_id
            
        except Exception as e:
            self.logger.error(f"启动脚本执行失败: {e}")
            if finished_callback:
                finished_callback({
                    'success': False,
                    'error': str(e),
                    'execution_time': 0
                })
            raise
            
    def _execute_script_thread(self, execution_id: str, script_path: Path, 
                              config: Dict[str, Any],
                              output_callback: Callable[[str, str], None],
                              finished_callback: Callable[[Dict[str, Any]], None]):
        """脚本执行线程"""
        start_time = time.time()
        
        try:
            # 构建执行命令
            cmd = self._build_command(script_path, config)
            
            # 设置工作目录
            working_dir = config.get('working_directory', script_path.parent)
            
            # 设置环境变量
            env = os.environ.copy()
            env_vars = config.get('environment_variables', {})
            env.update(env_vars)
            
            if output_callback:
                output_callback(f"执行命令: {' '.join(cmd)}", "info")
                output_callback(f"工作目录: {working_dir}", "info")
                
            # 启动进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True,
                cwd=working_dir,
                env=env
            )
            
            # 存储进程信息
            self.running_processes[execution_id] = {
                'process': process,
                'start_time': start_time,
                'script_path': str(script_path)
            }
            
            # 实时读取输出
            output_lines = []
            error_lines = []
            
            # 读取标准输出
            def read_stdout():
                for line in iter(process.stdout.readline, ''):
                    if line:
                        line = line.rstrip('\n\r')
                        output_lines.append(line)
                        if output_callback:
                            output_callback(line, "stdout")
                            
            # 读取错误输出
            def read_stderr():
                for line in iter(process.stderr.readline, ''):
                    if line:
                        line = line.rstrip('\n\r')
                        error_lines.append(line)
                        if output_callback:
                            output_callback(line, "stderr")
                            
            # 启动输出读取线程
            stdout_thread = threading.Thread(target=read_stdout, daemon=True)
            stderr_thread = threading.Thread(target=read_stderr, daemon=True)
            
            stdout_thread.start()
            stderr_thread.start()
            
            # 等待进程完成
            timeout = config.get('timeout', 300)  # 默认5分钟超时
            try:
                return_code = process.wait(timeout=timeout)
            except subprocess.TimeoutExpired:
                process.kill()
                return_code = -1
                if output_callback:
                    output_callback(f"脚本执行超时 ({timeout}秒)，已强制终止", "error")
                    
            # 等待输出读取完成
            stdout_thread.join(timeout=1)
            stderr_thread.join(timeout=1)
            
            # 计算执行时间
            execution_time = time.time() - start_time
            
            # 清理进程记录
            if execution_id in self.running_processes:
                del self.running_processes[execution_id]
                
            # 准备结果
            result = {
                'success': return_code == 0,
                'return_code': return_code,
                'execution_time': execution_time,
                'output': '\n'.join(output_lines),
                'error_output': '\n'.join(error_lines),
                'script_path': str(script_path),
                'execution_id': execution_id
            }
            
            if return_code == 0:
                self.logger.info(f"脚本执行成功: {script_path} (耗时: {execution_time:.2f}秒)")
                if output_callback:
                    output_callback(f"脚本执行完成，耗时: {execution_time:.2f}秒", "success")
            else:
                self.logger.error(f"脚本执行失败: {script_path} (返回码: {return_code})")
                if output_callback:
                    output_callback(f"脚本执行失败，返回码: {return_code}", "error")
                    
            # 调用完成回调
            if finished_callback:
                finished_callback(result)
                
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"脚本执行异常: {e}")
            
            # 清理进程记录
            if execution_id in self.running_processes:
                del self.running_processes[execution_id]
                
            if output_callback:
                output_callback(f"脚本执行异常: {e}", "error")
                
            if finished_callback:
                finished_callback({
                    'success': False,
                    'error': str(e),
                    'execution_time': execution_time,
                    'script_path': str(script_path),
                    'execution_id': execution_id
                })
                
    def _build_command(self, script_path: Path, config: Dict[str, Any]) -> list:
        """构建执行命令"""
        cmd = [self.python_executable, str(script_path)]
        
        # 添加脚本参数
        args = config.get('arguments', [])
        if isinstance(args, str):
            # 简单的参数字符串分割
            args = args.split()
        cmd.extend(args)
        
        return cmd
        
    def stop_execution(self, execution_id: str) -> bool:
        """停止脚本执行"""
        try:
            if execution_id in self.running_processes:
                process_info = self.running_processes[execution_id]
                process = process_info['process']
                
                if process.poll() is None:  # 进程还在运行
                    process.terminate()
                    
                    # 等待进程结束
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        # 强制杀死进程
                        process.kill()
                        process.wait()
                        
                    self.logger.info(f"已停止脚本执行: {execution_id}")
                    return True
                else:
                    self.logger.warning(f"脚本已经结束: {execution_id}")
                    return False
            else:
                self.logger.warning(f"未找到执行记录: {execution_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"停止脚本执行失败: {e}")
            return False
            
    def get_running_executions(self) -> Dict[str, Dict[str, Any]]:
        """获取正在运行的执行"""
        running = {}
        for exec_id, info in self.running_processes.items():
            if info['process'].poll() is None:  # 进程还在运行
                running[exec_id] = {
                    'script_path': info['script_path'],
                    'start_time': info['start_time'],
                    'duration': time.time() - info['start_time']
                }
        return running
        
    def is_running(self, execution_id: str) -> bool:
        """检查执行是否还在运行"""
        if execution_id in self.running_processes:
            process = self.running_processes[execution_id]['process']
            return process.poll() is None
        return False
