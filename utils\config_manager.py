#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
管理应用程序的配置信息
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from utils.logger import get_logger


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = Path(config_file)
        self.logger = get_logger(__name__)
        self.config = self._load_default_config()
        self.load_config()
        
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            "app": {
                "name": "PyScriptRunner",
                "version": "1.0.0",
                "description": "Python一体化脚本执行器",
                "author": "PyScriptRunner Team"
            },
            "settings": {
                "max_log_lines": 1000,
                "auto_scroll_logs": True,
                "check_dependencies_on_import": True,
                "auto_install_dependencies": False,
                "default_python_interpreter": "python",
                "script_timeout": 300,
                "max_concurrent_scripts": 3,
                "remember_window_size": True,
                "remember_imported_paths": True
            },
            "ui": {
                "window_width": 1200,
                "window_height": 800,
                "splitter_left_width": 300,
                "splitter_right_width": 900,
                "theme": "default",
                "font_family": "Consolas",
                "font_size": 10
            },
            "paths": {
                "data_dir": "data",
                "logs_dir": "data/logs",
                "environments_dir": "data/environments",
                "scripts_db": "data/scripts.db",
                "temp_dir": "data/temp",
                "imported_paths": []  # 记录导入的路径
            },
            "pip": {
                "index_url": "https://pypi.tuna.tsinghua.edu.cn/simple/",
                "trusted_hosts": ["pypi.tuna.tsinghua.edu.cn"],
                "timeout": 60,
                "retries": 3
            },
            "recent": {
                "imported_folders": [],  # 最近导入的文件夹
                "imported_files": [],    # 最近导入的文件
                "executed_scripts": [],  # 最近执行的脚本
                "max_recent_items": 10
            }
        }
        
    def load_config(self):
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    
                # 合并配置（保留默认值，覆盖已存在的值）
                self._merge_config(self.config, file_config)
                self.logger.info(f"配置文件加载成功: {self.config_file}")
            else:
                self.logger.info("配置文件不存在，使用默认配置")
                self.save_config()  # 创建默认配置文件
                
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            
    def save_config(self):
        """保存配置文件"""
        try:
            # 确保目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
                
            self.logger.debug("配置文件保存成功")
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            
    def _merge_config(self, default: Dict, loaded: Dict):
        """递归合并配置"""
        for key, value in loaded.items():
            if key in default:
                if isinstance(default[key], dict) and isinstance(value, dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value
            else:
                default[key] = value
                
    def get(self, key_path: str, default=None):
        """
        获取配置值
        
        Args:
            key_path: 配置路径，如 'settings.max_log_lines'
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key_path.split('.')
            value = self.config
            
            for key in keys:
                value = value[key]
                
            return value
            
        except (KeyError, TypeError):
            return default
            
    def set(self, key_path: str, value: Any, save: bool = True):
        """
        设置配置值
        
        Args:
            key_path: 配置路径
            value: 配置值
            save: 是否立即保存到文件
        """
        try:
            keys = key_path.split('.')
            config = self.config
            
            # 导航到父级
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
                
            # 设置值
            config[keys[-1]] = value
            
            if save:
                self.save_config()
                
        except Exception as e:
            self.logger.error(f"设置配置失败: {e}")
            
    def add_imported_path(self, path: str, path_type: str = "folder"):
        """
        添加导入路径到历史记录
        
        Args:
            path: 文件或文件夹路径
            path_type: 类型 ('folder' 或 'file')
        """
        try:
            path = str(Path(path).absolute())
            
            if path_type == "folder":
                recent_list = self.get("recent.imported_folders", [])
            else:
                recent_list = self.get("recent.imported_files", [])
                
            # 移除已存在的路径
            if path in recent_list:
                recent_list.remove(path)
                
            # 添加到开头
            recent_list.insert(0, path)
            
            # 限制数量
            max_items = self.get("recent.max_recent_items", 10)
            recent_list = recent_list[:max_items]
            
            # 保存
            if path_type == "folder":
                self.set("recent.imported_folders", recent_list)
            else:
                self.set("recent.imported_files", recent_list)
                
            # 同时添加到导入路径列表
            imported_paths = self.get("paths.imported_paths", [])
            if path not in imported_paths:
                imported_paths.append(path)
                self.set("paths.imported_paths", imported_paths)
                
        except Exception as e:
            self.logger.error(f"添加导入路径失败: {e}")
            
    def get_recent_imported_folders(self) -> List[str]:
        """获取最近导入的文件夹"""
        return self.get("recent.imported_folders", [])
        
    def get_recent_imported_files(self) -> List[str]:
        """获取最近导入的文件"""
        return self.get("recent.imported_files", [])
        
    def get_imported_paths(self) -> List[str]:
        """获取所有导入的路径"""
        return self.get("paths.imported_paths", [])
        
    def remove_imported_path(self, path: str):
        """移除导入路径"""
        try:
            path = str(Path(path).absolute())
            
            # 从导入路径列表移除
            imported_paths = self.get("paths.imported_paths", [])
            if path in imported_paths:
                imported_paths.remove(path)
                self.set("paths.imported_paths", imported_paths)
                
            # 从最近文件夹移除
            recent_folders = self.get("recent.imported_folders", [])
            if path in recent_folders:
                recent_folders.remove(path)
                self.set("recent.imported_folders", recent_folders)
                
            # 从最近文件移除
            recent_files = self.get("recent.imported_files", [])
            if path in recent_files:
                recent_files.remove(path)
                self.set("recent.imported_files", recent_files)
                
        except Exception as e:
            self.logger.error(f"移除导入路径失败: {e}")
            
    def add_executed_script(self, script_path: str):
        """添加执行的脚本到历史记录"""
        try:
            script_path = str(Path(script_path).absolute())
            recent_scripts = self.get("recent.executed_scripts", [])
            
            # 移除已存在的
            if script_path in recent_scripts:
                recent_scripts.remove(script_path)
                
            # 添加到开头
            recent_scripts.insert(0, script_path)
            
            # 限制数量
            max_items = self.get("recent.max_recent_items", 10)
            recent_scripts = recent_scripts[:max_items]
            
            self.set("recent.executed_scripts", recent_scripts)
            
        except Exception as e:
            self.logger.error(f"添加执行脚本失败: {e}")
            
    def get_recent_executed_scripts(self) -> List[str]:
        """获取最近执行的脚本"""
        return self.get("recent.executed_scripts", [])
        
    def update_window_geometry(self, width: int, height: int):
        """更新窗口几何信息"""
        if self.get("settings.remember_window_size", True):
            self.set("ui.window_width", width, save=False)
            self.set("ui.window_height", height, save=True)
            
    def get_window_geometry(self) -> tuple:
        """获取窗口几何信息"""
        width = self.get("ui.window_width", 1200)
        height = self.get("ui.window_height", 800)
        return width, height
