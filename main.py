#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyScriptRunner - Python一体化脚本执行器
主程序入口文件
"""

import sys
import os
from pathlib import Path
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from gui.main_window import MainWindow
from utils.logger import setup_logger


def setup_application():
    """设置应用程序基本配置"""
    # 创建应用程序实例
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("PyScriptRunner")
    app.setApplicationDisplayName("Python脚本执行器")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("PyScriptRunner")
    app.setOrganizationDomain("pyscriptrunner.com")
    
    # 设置高DPI支持 (PyQt6中默认启用)
    # app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
    # app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    
    # 设置应用程序图标
    icon_path = project_root / "resources" / "icons" / "app.ico"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    return app


def create_directories():
    """创建必要的目录结构"""
    directories = [
        "data",
        "data/logs",
        "data/environments",
        "resources",
        "resources/icons",
        "resources/styles",
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(parents=True, exist_ok=True)


def main():
    """主函数"""
    try:
        # 创建必要的目录
        create_directories()
        
        # 设置日志系统
        logger = setup_logger()
        logger.info("PyScriptRunner 启动中...")
        
        # 创建应用程序
        app = setup_application()
        
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()
        
        logger.info("PyScriptRunner 启动完成")
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
