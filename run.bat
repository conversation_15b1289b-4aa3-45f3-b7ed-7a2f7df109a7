@echo off
chcp 65001 > nul
echo ========================================
echo   PyScriptRunner - Python脚本执行器
echo ========================================
echo.
echo 正在启动程序...
echo.

REM 检查Python是否安装
python --version > nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python解释器
    echo 请确保已安装Python 3.8+并添加到PATH环境变量
    pause
    exit /b 1
)

REM 检查PyQt6是否安装
python -c "import PyQt6" > nul 2>&1
if errorlevel 1 (
    echo 警告: 未找到PyQt6库
    echo 正在尝试安装依赖...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
)

REM 启动程序
python main.py

if errorlevel 1 (
    echo.
    echo 程序异常退出
    pause
)
