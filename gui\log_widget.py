#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志显示组件
显示脚本执行日志和系统消息
"""

import datetime
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, 
    QLabel, QPushButton, QComboBox, QCheckBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QTextCursor, QColor

from utils.logger import get_logger


class LogWidget(QWidget):
    """日志显示组件"""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.max_lines = 1000  # 最大日志行数
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 标题和控制栏
        header_layout = QHBoxLayout()
        
        # 标题
        title_label = QLabel("📋 执行日志")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; padding: 5px;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # 日志级别过滤
        level_label = QLabel("级别:")
        header_layout.addWidget(level_label)
        
        self.level_combo = QComboBox()
        self.level_combo.addItems(["全部", "INFO", "WARNING", "ERROR", "DEBUG"])
        self.level_combo.currentTextChanged.connect(self.filter_logs)
        header_layout.addWidget(self.level_combo)
        
        # 自动滚动
        self.auto_scroll_cb = QCheckBox("自动滚动")
        self.auto_scroll_cb.setChecked(True)
        header_layout.addWidget(self.auto_scroll_cb)
        
        # 清除按钮
        clear_btn = QPushButton("🗑️ 清除")
        clear_btn.clicked.connect(self.clear_logs)
        header_layout.addWidget(clear_btn)
        
        # 导出按钮
        export_btn = QPushButton("💾 导出")
        export_btn.clicked.connect(self.export_logs)
        header_layout.addWidget(export_btn)
        
        layout.addLayout(header_layout)
        
        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        
        # 设置等宽字体
        font = QFont("Consolas", 9)
        font.setStyleHint(QFont.StyleHint.Monospace)
        self.log_text.setFont(font)
        
        # 设置样式
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #3c3c3c;
                border-radius: 4px;
                padding: 5px;
            }
        """)
        
        layout.addWidget(self.log_text)
        
        # 状态栏
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: gray; font-size: 12px;")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        self.line_count_label = QLabel("行数: 0")
        self.line_count_label.setStyleSheet("color: gray; font-size: 12px;")
        status_layout.addWidget(self.line_count_label)
        
        layout.addLayout(status_layout)
        
        # 添加欢迎消息
        self.add_welcome_message()
        
    def add_welcome_message(self):
        """添加欢迎消息"""
        welcome_msg = """
=== PyScriptRunner 日志系统 ===
时间: {time}
版本: 1.0.0
状态: 就绪

📋 使用说明:
• 导入脚本后，相关日志将在此显示
• 可以通过级别过滤器筛选日志
• 支持自动滚动到最新日志
• 可以导出日志到文件

等待操作...
        """.format(time=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        self.log_text.setPlainText(welcome_msg.strip())
        self.update_line_count()
        
    def add_log(self, level, message, timestamp=None):
        """
        添加日志条目
        
        Args:
            level: 日志级别 (INFO, WARNING, ERROR, DEBUG)
            message: 日志消息
            timestamp: 时间戳，默认为当前时间
        """
        if timestamp is None:
            timestamp = datetime.datetime.now()
            
        # 格式化时间
        time_str = timestamp.strftime('%H:%M:%S')
        
        # 格式化日志条目
        log_entry = f"[{time_str}] [{level:>7}] {message}"
        
        # 添加到文本框
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        
        # 设置颜色
        color = self.get_level_color(level)
        self.log_text.setTextColor(color)
        
        # 插入日志
        cursor.insertText(log_entry + '\n')
        
        # 限制行数
        self.limit_lines()
        
        # 自动滚动
        if self.auto_scroll_cb.isChecked():
            self.scroll_to_bottom()
            
        # 更新状态
        self.update_line_count()
        self.status_label.setText(f"最后更新: {time_str}")
        
        # 记录到文件日志
        self.logger.log(self.get_logging_level(level), message)
        
    def get_level_color(self, level):
        """获取日志级别对应的颜色"""
        colors = {
            'INFO': QColor('#ffffff'),      # 白色
            'WARNING': QColor('#ffa500'),   # 橙色
            'ERROR': QColor('#ff4444'),     # 红色
            'DEBUG': QColor('#888888'),     # 灰色
            'SUCCESS': QColor('#00ff00'),   # 绿色
        }
        return colors.get(level, QColor('#ffffff'))
        
    def get_logging_level(self, level):
        """获取Python logging级别"""
        import logging
        levels = {
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'DEBUG': logging.DEBUG,
        }
        return levels.get(level, logging.INFO)
        
    def limit_lines(self):
        """限制日志行数"""
        document = self.log_text.document()
        if document.blockCount() > self.max_lines:
            cursor = QTextCursor(document)
            cursor.movePosition(QTextCursor.MoveOperation.Start)
            cursor.movePosition(
                QTextCursor.MoveOperation.Down, 
                QTextCursor.MoveMode.KeepAnchor, 
                document.blockCount() - self.max_lines
            )
            cursor.removeSelectedText()
            
    def scroll_to_bottom(self):
        """滚动到底部"""
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def update_line_count(self):
        """更新行数显示"""
        line_count = self.log_text.document().blockCount()
        self.line_count_label.setText(f"行数: {line_count}")
        
    def filter_logs(self, level):
        """过滤日志"""
        # TODO: 实现日志过滤功能
        # 这里需要保存原始日志数据，然后根据级别过滤显示
        pass
        
    def clear_logs(self):
        """清除日志"""
        self.log_text.clear()
        self.update_line_count()
        self.status_label.setText("日志已清除")
        self.logger.info("用户清除了日志显示")
        
    def export_logs(self):
        """导出日志"""
        from PyQt6.QtWidgets import QFileDialog
        
        try:
            # 选择保存文件
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出日志",
                f"pyscriptrunner_log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                "文本文件 (*.txt);;所有文件 (*)"
            )
            
            if file_path:
                # 保存日志内容
                content = self.log_text.toPlainText()
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
                self.add_log('INFO', f'日志已导出到: {file_path}')
                
        except Exception as e:
            self.add_log('ERROR', f'导出日志失败: {e}')
            
    # 便捷方法
    def log_info(self, message):
        """记录INFO级别日志"""
        self.add_log('INFO', message)
        
    def log_warning(self, message):
        """记录WARNING级别日志"""
        self.add_log('WARNING', message)
        
    def log_error(self, message):
        """记录ERROR级别日志"""
        self.add_log('ERROR', message)
        
    def log_debug(self, message):
        """记录DEBUG级别日志"""
        self.add_log('DEBUG', message)
        
    def log_success(self, message):
        """记录SUCCESS级别日志"""
        self.add_log('SUCCESS', message)
