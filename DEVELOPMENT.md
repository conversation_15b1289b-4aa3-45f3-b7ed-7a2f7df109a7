# 🛠️ PyScriptRunner 开发指南

## 📋 当前实现状态

### ✅ 已完成功能

1. **基础框架**
   - ✅ 项目结构搭建
   - ✅ 主窗口界面 (MainWindow)
   - ✅ 脚本列表组件 (ScriptListWidget)
   - ✅ 脚本详情组件 (ScriptDetailWidget)
   - ✅ 日志显示组件 (LogWidget)
   - ✅ 日志系统 (Logger)

2. **脚本管理**
   - ✅ 文件夹扫描和脚本导入
   - ✅ 脚本信息提取（名称、描述、大小等）
   - ✅ 树形列表显示
   - ✅ 脚本选择和详情显示
   - ✅ 右键菜单（基础）

3. **用户界面**
   - ✅ 响应式布局设计
   - ✅ 菜单栏和工具栏
   - ✅ 状态栏
   - ✅ 分割器布局
   - ✅ 基础样式文件

4. **配置系统**
   - ✅ 配置文件结构 (config.json)
   - ✅ 启动脚本 (run.bat)

### 🔄 进行中功能

1. **依赖管理**
   - ⏳ 依赖检测（AST分析）
   - ⏳ 依赖安装
   - ⏳ 虚拟环境管理

2. **脚本执行**
   - ⏳ 脚本执行引擎
   - ⏳ 实时日志捕获
   - ⏳ 进程管理

### 📅 待实现功能

1. **核心功能**
   - [ ] 脚本配置对话框
   - [ ] 脚本执行器
   - [ ] 依赖分析器
   - [ ] 包管理器
   - [ ] 数据库存储

2. **高级功能**
   - [ ] 定时任务
   - [ ] 批量操作
   - [ ] 搜索和过滤
   - [ ] 脚本编辑器
   - [ ] 执行历史

3. **系统功能**
   - [ ] 设置对话框
   - [ ] 主题切换
   - [ ] 插件系统
   - [ ] 自动更新

## 🏗️ 架构设计

### 模块划分

```
PyScriptRunner/
├── main.py                 # 程序入口
├── core/                   # 核心业务逻辑
│   ├── script_manager.py   # 脚本管理器
│   ├── dependency_analyzer.py  # 依赖分析器
│   ├── package_manager.py  # 包管理器
│   ├── script_executor.py  # 脚本执行器
│   └── database.py         # 数据库操作
├── gui/                    # 用户界面
│   ├── main_window.py      # 主窗口
│   ├── script_list_widget.py   # 脚本列表
│   ├── script_detail_widget.py # 脚本详情
│   ├── config_dialog.py    # 配置对话框
│   └── log_widget.py       # 日志组件
└── utils/                  # 工具模块
    ├── logger.py           # 日志工具
    ├── file_utils.py       # 文件工具
    └── config.py           # 配置管理
```

### 数据流设计

```
用户操作 → GUI组件 → 信号/槽 → 核心模块 → 数据处理 → 界面更新
```

## 🔧 开发环境

### 环境要求

- Python 3.8+
- PyQt6 6.4.0+
- Windows 10/11 (主要目标平台)

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行程序

```bash
# 方式1: 直接运行
python main.py

# 方式2: 使用批处理文件
run.bat
```

## 📝 编码规范

### Python代码规范

1. **PEP 8** 代码风格
2. **类型注解** 使用类型提示
3. **文档字符串** 所有公共方法都要有文档
4. **异常处理** 适当的异常捕获和处理
5. **日志记录** 关键操作都要记录日志

### 示例代码

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块文档字符串
简要描述模块功能
"""

from typing import Optional, List
from utils.logger import get_logger


class ExampleClass:
    """示例类"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
    def example_method(self, param: str) -> Optional[str]:
        """
        示例方法
        
        Args:
            param: 参数描述
            
        Returns:
            返回值描述
            
        Raises:
            ValueError: 异常描述
        """
        try:
            self.logger.info(f"执行示例方法: {param}")
            # 业务逻辑
            return "result"
        except Exception as e:
            self.logger.error(f"方法执行失败: {e}")
            raise
```

### GUI组件规范

1. **信号/槽机制** 使用PyQt6的信号槽进行组件通信
2. **布局管理** 使用布局管理器，避免固定位置
3. **样式分离** 样式写在QSS文件中
4. **国际化支持** 预留多语言支持接口

## 🧪 测试

### 测试脚本

项目包含三个测试脚本：

1. **sample1.py** - 基础功能测试
2. **sample2.py** - 文件操作测试  
3. **sample3.py** - 第三方依赖测试

### 测试流程

1. 启动程序
2. 导入 `test_scripts` 文件夹
3. 选择脚本查看详情
4. 测试各项功能

## 🚀 下一步开发计划

### 第一优先级（核心功能）

1. **实现依赖分析器**
   ```python
   # core/dependency_analyzer.py
   class DependencyAnalyzer:
       def analyze_script(self, script_path: str) -> List[str]:
           """分析脚本依赖"""
           pass
   ```

2. **实现脚本执行器**
   ```python
   # core/script_executor.py
   class ScriptExecutor:
       def execute_script(self, script_path: str, config: dict):
           """执行脚本"""
           pass
   ```

3. **实现配置对话框**
   ```python
   # gui/config_dialog.py
   class ConfigDialog(QDialog):
       """脚本配置对话框"""
       pass
   ```

### 第二优先级（增强功能）

1. 数据库存储
2. 执行历史
3. 搜索过滤
4. 批量操作

### 第三优先级（高级功能）

1. 定时任务
2. 插件系统
3. 主题切换
4. 自动更新

## 📚 参考资料

- [PyQt6 官方文档](https://doc.qt.io/qtforpython/)
- [Python AST 模块](https://docs.python.org/3/library/ast.html)
- [Python subprocess 模块](https://docs.python.org/3/library/subprocess.html)
- [SQLite 数据库](https://docs.python.org/3/library/sqlite3.html)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 编写代码和测试
4. 提交 Pull Request

## 📞 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [Issues]
