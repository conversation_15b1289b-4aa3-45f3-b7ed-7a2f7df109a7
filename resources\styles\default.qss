/* PyScriptRunner 默认样式 */

/* 主窗口 */
QMainWindow {
    background-color: #f5f5f5;
}

/* 工具栏 */
QToolBar {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 4px;
    spacing: 8px;
}

QToolBar QPushButton {
    background-color: #ffffff;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
}

QToolBar QPushButton:hover {
    background-color: #e3f2fd;
    border-color: #2196f3;
}

QToolBar QPushButton:pressed {
    background-color: #bbdefb;
}

/* 状态栏 */
QStatusBar {
    background-color: #ffffff;
    border-top: 1px solid #e0e0e0;
    padding: 4px;
}

/* 分组框 */
QGroupBox {
    font-weight: bold;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    margin-top: 1ex;
    padding-top: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
    color: #333333;
}

/* 按钮 */
QPushButton {
    background-color: #ffffff;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 12px;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #e3f2fd;
    border-color: #2196f3;
}

QPushButton:pressed {
    background-color: #bbdefb;
}

QPushButton:disabled {
    background-color: #f5f5f5;
    color: #999999;
    border-color: #e0e0e0;
}

/* 输入框 */
QLineEdit {
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    padding: 6px;
    font-size: 12px;
    background-color: #ffffff;
}

QLineEdit:focus {
    border-color: #2196f3;
    outline: none;
}

/* 文本编辑器 */
QTextEdit {
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    background-color: #ffffff;
    font-family: "Consolas", "Monaco", monospace;
    font-size: 11px;
}

/* 树形控件 */
QTreeWidget {
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    background-color: #ffffff;
    alternate-background-color: #f9f9f9;
    selection-background-color: #e3f2fd;
}

QTreeWidget::item {
    padding: 4px;
    border: none;
}

QTreeWidget::item:hover {
    background-color: #f0f0f0;
}

QTreeWidget::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

/* 下拉框 */
QComboBox {
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    padding: 4px 8px;
    background-color: #ffffff;
    min-width: 80px;
}

QComboBox:hover {
    border-color: #2196f3;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(down_arrow.png);
    width: 12px;
    height: 12px;
}

/* 复选框 */
QCheckBox {
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #d0d0d0;
    border-radius: 3px;
    background-color: #ffffff;
}

QCheckBox::indicator:checked {
    background-color: #2196f3;
    border-color: #2196f3;
}

/* 标签 */
QLabel {
    color: #333333;
}

/* 分割器 */
QSplitter::handle {
    background-color: #e0e0e0;
}

QSplitter::handle:horizontal {
    width: 2px;
}

QSplitter::handle:vertical {
    height: 2px;
}

/* 滚动条 */
QScrollBar:vertical {
    background-color: #f0f0f0;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #c0c0c0;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #a0a0a0;
}

QScrollBar:horizontal {
    background-color: #f0f0f0;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #c0c0c0;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #a0a0a0;
}
