#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库操作模块
管理脚本信息的持久化存储
"""

import sqlite3
import json
import datetime
from pathlib import Path
from typing import List, Dict, Optional, Any
from utils.logger import get_logger


class ScriptDatabase:
    """脚本数据库管理器"""
    
    def __init__(self, db_path: str = "data/scripts.db"):
        self.db_path = Path(db_path)
        self.logger = get_logger(__name__)
        self.init_database()
        
    def init_database(self):
        """初始化数据库"""
        try:
            # 确保数据目录存在
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建脚本表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS scripts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        path TEXT UNIQUE NOT NULL,
                        name TEXT NOT NULL,
                        folder TEXT NOT NULL,
                        description TEXT,
                        size INTEGER,
                        modified_time REAL,
                        created_time REAL DEFAULT (datetime('now')),
                        last_accessed REAL DEFAULT (datetime('now')),
                        status TEXT DEFAULT 'active',
                        dependencies TEXT,  -- JSON格式存储依赖列表
                        config TEXT,        -- JSON格式存储配置信息
                        tags TEXT,          -- JSON格式存储标签
                        execution_count INTEGER DEFAULT 0,
                        last_execution REAL,
                        favorite INTEGER DEFAULT 0
                    )
                ''')
                
                # 创建执行历史表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS execution_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        script_id INTEGER,
                        start_time REAL,
                        end_time REAL,
                        duration REAL,
                        status TEXT,  -- success, error, timeout
                        exit_code INTEGER,
                        output TEXT,
                        error_output TEXT,
                        config TEXT,  -- 执行时的配置
                        FOREIGN KEY (script_id) REFERENCES scripts (id)
                    )
                ''')
                
                # 创建依赖表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS dependencies (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        script_id INTEGER,
                        package_name TEXT,
                        version TEXT,
                        is_installed INTEGER DEFAULT 0,
                        install_time REAL,
                        FOREIGN KEY (script_id) REFERENCES scripts (id)
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_scripts_path ON scripts(path)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_scripts_folder ON scripts(folder)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_execution_script_id ON execution_history(script_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_dependencies_script_id ON dependencies(script_id)')
                
                conn.commit()
                self.logger.info("数据库初始化完成")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
            
    def add_script(self, script_info: Dict[str, Any]) -> int:
        """
        添加脚本到数据库
        
        Args:
            script_info: 脚本信息字典
            
        Returns:
            int: 脚本ID
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查脚本是否已存在
                cursor.execute('SELECT id FROM scripts WHERE path = ?', (script_info['path'],))
                existing = cursor.fetchone()
                
                if existing:
                    # 更新现有脚本
                    script_id = existing[0]
                    self.update_script(script_id, script_info)
                    return script_id
                
                # 插入新脚本
                cursor.execute('''
                    INSERT INTO scripts (
                        path, name, folder, description, size, modified_time,
                        dependencies, config, tags
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    script_info['path'],
                    script_info['name'],
                    script_info['folder'],
                    script_info.get('description', ''),
                    script_info.get('size', 0),
                    script_info.get('modified', 0),
                    json.dumps(script_info.get('dependencies', [])),
                    json.dumps(script_info.get('config', {})),
                    json.dumps(script_info.get('tags', []))
                ))
                
                script_id = cursor.lastrowid
                conn.commit()
                
                self.logger.info(f"添加脚本到数据库: {script_info['name']} (ID: {script_id})")
                return script_id
                
        except Exception as e:
            self.logger.error(f"添加脚本失败: {e}")
            raise
            
    def update_script(self, script_id: int, script_info: Dict[str, Any]):
        """更新脚本信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE scripts SET
                        name = ?, folder = ?, description = ?, size = ?,
                        modified_time = ?, last_accessed = datetime('now'),
                        dependencies = ?, config = ?, tags = ?
                    WHERE id = ?
                ''', (
                    script_info['name'],
                    script_info['folder'],
                    script_info.get('description', ''),
                    script_info.get('size', 0),
                    script_info.get('modified', 0),
                    json.dumps(script_info.get('dependencies', [])),
                    json.dumps(script_info.get('config', {})),
                    json.dumps(script_info.get('tags', [])),
                    script_id
                ))
                
                conn.commit()
                self.logger.info(f"更新脚本信息: ID {script_id}")
                
        except Exception as e:
            self.logger.error(f"更新脚本失败: {e}")
            raise
            
    def get_all_scripts(self) -> List[Dict[str, Any]]:
        """获取所有脚本"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM scripts 
                    WHERE status = 'active'
                    ORDER BY folder, name
                ''')
                
                scripts = []
                for row in cursor.fetchall():
                    script = dict(row)
                    # 解析JSON字段
                    script['dependencies'] = json.loads(script['dependencies'] or '[]')
                    script['config'] = json.loads(script['config'] or '{}')
                    script['tags'] = json.loads(script['tags'] or '[]')
                    scripts.append(script)
                    
                return scripts
                
        except Exception as e:
            self.logger.error(f"获取脚本列表失败: {e}")
            return []
            
    def get_script_by_path(self, path: str) -> Optional[Dict[str, Any]]:
        """根据路径获取脚本"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('SELECT * FROM scripts WHERE path = ?', (path,))
                row = cursor.fetchone()
                
                if row:
                    script = dict(row)
                    script['dependencies'] = json.loads(script['dependencies'] or '[]')
                    script['config'] = json.loads(script['config'] or '{}')
                    script['tags'] = json.loads(script['tags'] or '[]')
                    return script
                    
                return None
                
        except Exception as e:
            self.logger.error(f"获取脚本失败: {e}")
            return None
            
    def remove_script(self, script_id: int):
        """移除脚本（软删除）"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE scripts SET status = 'deleted' WHERE id = ?
                ''', (script_id,))
                
                conn.commit()
                self.logger.info(f"移除脚本: ID {script_id}")
                
        except Exception as e:
            self.logger.error(f"移除脚本失败: {e}")
            raise
            
    def remove_script_by_path(self, path: str):
        """根据路径移除脚本"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE scripts SET status = 'deleted' WHERE path = ?
                ''', (path,))
                
                conn.commit()
                self.logger.info(f"移除脚本: {path}")
                
        except Exception as e:
            self.logger.error(f"移除脚本失败: {e}")
            raise
            
    def update_script_config(self, script_id: int, config: Dict[str, Any]):
        """更新脚本配置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE scripts SET config = ? WHERE id = ?
                ''', (json.dumps(config), script_id))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"更新脚本配置失败: {e}")
            raise
            
    def add_execution_record(self, script_id: int, execution_info: Dict[str, Any]) -> int:
        """添加执行记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO execution_history (
                        script_id, start_time, end_time, duration, status,
                        exit_code, output, error_output, config
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    script_id,
                    execution_info.get('start_time', 0),
                    execution_info.get('end_time', 0),
                    execution_info.get('duration', 0),
                    execution_info.get('status', 'unknown'),
                    execution_info.get('exit_code', -1),
                    execution_info.get('output', ''),
                    execution_info.get('error_output', ''),
                    json.dumps(execution_info.get('config', {}))
                ))
                
                record_id = cursor.lastrowid
                
                # 更新脚本的执行统计
                cursor.execute('''
                    UPDATE scripts SET 
                        execution_count = execution_count + 1,
                        last_execution = ?
                    WHERE id = ?
                ''', (execution_info.get('end_time', 0), script_id))
                
                conn.commit()
                return record_id
                
        except Exception as e:
            self.logger.error(f"添加执行记录失败: {e}")
            raise
            
    def get_execution_history(self, script_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """获取执行历史"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM execution_history 
                    WHERE script_id = ?
                    ORDER BY start_time DESC
                    LIMIT ?
                ''', (script_id, limit))
                
                history = []
                for row in cursor.fetchall():
                    record = dict(row)
                    record['config'] = json.loads(record['config'] or '{}')
                    history.append(record)
                    
                return history
                
        except Exception as e:
            self.logger.error(f"获取执行历史失败: {e}")
            return []
            
    def cleanup_missing_scripts(self):
        """清理不存在的脚本文件"""
        try:
            scripts = self.get_all_scripts()
            removed_count = 0
            
            for script in scripts:
                if not Path(script['path']).exists():
                    self.remove_script(script['id'])
                    removed_count += 1
                    
            if removed_count > 0:
                self.logger.info(f"清理了 {removed_count} 个不存在的脚本")
                
            return removed_count
            
        except Exception as e:
            self.logger.error(f"清理脚本失败: {e}")
            return 0
            
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 脚本总数
                cursor.execute("SELECT COUNT(*) FROM scripts WHERE status = 'active'")
                total_scripts = cursor.fetchone()[0]
                
                # 执行总数
                cursor.execute("SELECT COUNT(*) FROM execution_history")
                total_executions = cursor.fetchone()[0]
                
                # 最近执行的脚本
                cursor.execute('''
                    SELECT name, last_execution FROM scripts 
                    WHERE status = 'active' AND last_execution IS NOT NULL
                    ORDER BY last_execution DESC LIMIT 5
                ''')
                recent_scripts = cursor.fetchall()
                
                return {
                    'total_scripts': total_scripts,
                    'total_executions': total_executions,
                    'recent_scripts': recent_scripts
                }
                
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}
