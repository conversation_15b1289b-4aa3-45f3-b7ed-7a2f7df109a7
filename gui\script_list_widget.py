#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本列表组件
显示和管理Python脚本列表
"""

import os
from pathlib import Path
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTreeWidget, 
    QTreeWidgetItem, QLineEdit, QPushButton, QLabel,
    QMessageBox, QMenu
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon, QAction

from utils.logger import get_logger
from core.script_manager import ScriptManager


class ScriptListWidget(QWidget):
    """脚本列表组件"""
    
    # 信号定义
    script_selected = pyqtSignal(str)  # 脚本选择信号
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.script_manager = ScriptManager()
        self.scripts = {}  # 存储脚本信息 {path: info}
        self.init_ui()
        self.load_scripts_from_database()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 标题
        title_label = QLabel("📁 脚本列表")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; padding: 5px;")
        layout.addWidget(title_label)
        
        # 搜索框
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("🔍 搜索脚本...")
        self.search_input.textChanged.connect(self.filter_scripts)
        search_layout.addWidget(self.search_input)
        
        # 清除搜索按钮
        clear_btn = QPushButton("✕")
        clear_btn.setFixedSize(30, 30)
        clear_btn.setToolTip("清除搜索")
        clear_btn.clicked.connect(self.clear_search)
        search_layout.addWidget(clear_btn)
        
        layout.addLayout(search_layout)
        
        # 脚本树形列表
        self.script_tree = QTreeWidget()
        self.script_tree.setHeaderLabels(["脚本", "状态"])
        self.script_tree.setRootIsDecorated(True)
        self.script_tree.setAlternatingRowColors(True)
        self.script_tree.itemClicked.connect(self.on_item_clicked)
        self.script_tree.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.script_tree.customContextMenuRequested.connect(self.show_context_menu)
        layout.addWidget(self.script_tree)
        
        # 统计信息
        self.stats_label = QLabel("脚本总数: 0")
        self.stats_label.setStyleSheet("color: gray; font-size: 12px; padding: 5px;")
        layout.addWidget(self.stats_label)
        
    def import_scripts_from_folder(self, folder_path):
        """从文件夹导入脚本"""
        try:
            self.logger.info(f"开始导入脚本文件夹: {folder_path}")

            # 使用脚本管理器导入
            result = self.script_manager.import_scripts_from_folder(folder_path)

            if result['success']:
                # 重新加载脚本列表
                self.load_scripts_from_database()

                # 显示导入结果
                message = result['message']
                if result.get('errors'):
                    message += f"\n\n错误信息:\n" + "\n".join(result['errors'][:5])
                    if len(result['errors']) > 5:
                        message += f"\n... 还有 {len(result['errors']) - 5} 个错误"

                QMessageBox.information(self, "导入完成", message)
            else:
                QMessageBox.warning(self, "导入失败", result['message'])

        except Exception as e:
            self.logger.error(f"导入脚本失败: {e}")
            QMessageBox.critical(self, "错误", f"导入脚本失败:\n{e}")
            raise

    def load_scripts_from_database(self):
        """从数据库加载脚本列表"""
        try:
            self.logger.info("从数据库加载脚本列表")

            # 获取所有脚本
            scripts = self.script_manager.get_all_scripts()

            # 转换为原有格式
            self.scripts = {}
            for script in scripts:
                # 检查文件是否还存在
                if Path(script['path']).exists():
                    self.scripts[script['path']] = {
                        'path': script['path'],
                        'name': script['name'],
                        'folder': script['folder'],
                        'size': script['size'],
                        'modified': script['modified_time'],
                        'description': script['description'],
                        'status': '✅ 就绪',
                        'dependencies': script['dependencies']
                    }
                else:
                    # 文件不存在，标记为缺失
                    self.scripts[script['path']] = {
                        'path': script['path'],
                        'name': script['name'],
                        'folder': script['folder'],
                        'size': script['size'],
                        'modified': script['modified_time'],
                        'description': script['description'],
                        'status': '❌ 文件缺失',
                        'dependencies': script['dependencies']
                    }

            # 刷新界面
            self.refresh_tree()
            self.update_stats()

            self.logger.info(f"加载了 {len(self.scripts)} 个脚本")

        except Exception as e:
            self.logger.error(f"从数据库加载脚本失败: {e}")

    def add_script(self, script_path):
        """添加单个脚本"""
        try:
            script_path = Path(script_path)
            if not script_path.exists() or script_path.suffix != '.py':
                return False
                
            # 避免重复添加
            script_key = str(script_path.absolute())
            if script_key in self.scripts:
                return False
                
            # 提取脚本信息
            script_info = self.extract_script_info(script_path)
            self.scripts[script_key] = script_info
            
            self.logger.info(f"添加脚本: {script_path.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"添加脚本失败 {script_path}: {e}")
            return False
            
    def extract_script_info(self, script_path):
        """提取脚本信息"""
        info = {
            'path': str(script_path.absolute()),
            'name': script_path.name,
            'folder': script_path.parent.name,
            'size': script_path.stat().st_size,
            'modified': script_path.stat().st_mtime,
            'description': '',
            'status': '未检查',
            'dependencies': []
        }
        
        # 尝试读取脚本内容获取描述
        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # 查找文档字符串作为描述
            for i, line in enumerate(lines[:10]):  # 只检查前10行
                line = line.strip()
                if line.startswith('"""') or line.startswith("'''"):
                    # 找到文档字符串
                    if line.count('"""') == 2 or line.count("'''") == 2:
                        # 单行文档字符串
                        info['description'] = line.strip('"""').strip("'''").strip()
                    else:
                        # 多行文档字符串
                        desc_lines = [line.strip('"""').strip("'''")]
                        for j in range(i + 1, min(i + 5, len(lines))):
                            next_line = lines[j].strip()
                            if '"""' in next_line or "'''" in next_line:
                                desc_lines.append(next_line.split('"""')[0].split("'''")[0])
                                break
                            desc_lines.append(next_line)
                        info['description'] = ' '.join(desc_lines).strip()
                    break
                elif line.startswith('#') and not line.startswith('#!/'):
                    # 使用注释作为描述
                    info['description'] = line.lstrip('#').strip()
                    break
                    
        except Exception as e:
            self.logger.warning(f"读取脚本信息失败 {script_path}: {e}")
            
        return info
        
    def refresh_tree(self):
        """刷新树形列表"""
        self.script_tree.clear()
        
        # 按文件夹分组
        folders = {}
        for script_path, info in self.scripts.items():
            folder_name = info['folder']
            if folder_name not in folders:
                folders[folder_name] = []
            folders[folder_name].append(info)
            
        # 创建树形结构
        for folder_name, scripts in folders.items():
            folder_item = QTreeWidgetItem(self.script_tree)
            folder_item.setText(0, f"📁 {folder_name}")
            folder_item.setText(1, f"({len(scripts)} 个脚本)")
            folder_item.setExpanded(True)
            
            for script_info in scripts:
                script_item = QTreeWidgetItem(folder_item)
                script_item.setText(0, f"📄 {script_info['name']}")
                script_item.setText(1, script_info['status'])
                script_item.setData(0, Qt.ItemDataRole.UserRole, script_info['path'])
                
                # 设置工具提示
                tooltip = f"路径: {script_info['path']}\n"
                if script_info['description']:
                    tooltip += f"描述: {script_info['description']}\n"
                tooltip += f"大小: {script_info['size']} 字节"
                script_item.setToolTip(0, tooltip)
                
    def filter_scripts(self, text):
        """过滤脚本"""
        # TODO: 实现搜索过滤功能
        pass
        
    def clear_search(self):
        """清除搜索"""
        self.search_input.clear()
        
    def on_item_clicked(self, item, column):
        """处理项目点击事件"""
        script_path = item.data(0, Qt.ItemDataRole.UserRole)
        if script_path:
            self.script_selected.emit(script_path)
            
    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.script_tree.itemAt(position)
        if not item:
            return
            
        script_path = item.data(0, Qt.ItemDataRole.UserRole)
        if not script_path:
            return
            
        menu = QMenu(self)
        
        # 执行脚本
        run_action = QAction("▶️ 执行脚本", self)
        run_action.triggered.connect(lambda: self.run_script(script_path))
        menu.addAction(run_action)
        
        # 编辑脚本
        edit_action = QAction("✏️ 编辑脚本", self)
        edit_action.triggered.connect(lambda: self.edit_script(script_path))
        menu.addAction(edit_action)
        
        menu.addSeparator()
        
        # 删除脚本
        delete_action = QAction("🗑️ 从列表移除", self)
        delete_action.triggered.connect(lambda: self.remove_script(script_path))
        menu.addAction(delete_action)
        
        menu.exec(self.script_tree.mapToGlobal(position))
        
    def run_script(self, script_path):
        """执行脚本"""
        self.logger.info(f"执行脚本: {script_path}")
        # TODO: 实现脚本执行功能
        
    def edit_script(self, script_path):
        """编辑脚本"""
        self.logger.info(f"编辑脚本: {script_path}")
        # TODO: 实现脚本编辑功能
        
    def remove_script(self, script_path):
        """从列表移除脚本"""
        try:
            # 从数据库移除
            self.script_manager.remove_script(script_path)

            # 从内存中移除
            if script_path in self.scripts:
                del self.scripts[script_path]

            self.refresh_tree()
            self.update_stats()
            self.logger.info(f"移除脚本: {script_path}")

        except Exception as e:
            self.logger.error(f"移除脚本失败: {e}")
            QMessageBox.warning(self, "警告", f"移除脚本失败: {e}")
            
    def update_stats(self):
        """更新统计信息"""
        count = len(self.scripts)
        self.stats_label.setText(f"脚本总数: {count}")
        
    def refresh(self):
        """刷新列表"""
        try:
            # 清理不存在的脚本
            removed_count = self.script_manager.refresh_scripts()

            # 重新加载脚本列表
            self.load_scripts_from_database()

            if removed_count > 0:
                self.logger.info(f"刷新时清理了 {removed_count} 个不存在的脚本")

        except Exception as e:
            self.logger.error(f"刷新脚本列表失败: {e}")
