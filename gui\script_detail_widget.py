#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本详情组件
显示选中脚本的详细信息和操作按钮
"""

import os
from pathlib import Path
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QTextEdit, QGroupBox, QScrollArea,
    QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from utils.logger import get_logger
from core.dependency_analyzer import DependencyAnalyzer
from core.package_manager import PackageManager
from core.script_executor import ScriptExecutor


class ScriptDetailWidget(QWidget):
    """脚本详情组件"""
    
    # 信号定义
    script_executed = pyqtSignal(str)  # 脚本执行信号
    script_configured = pyqtSignal(str)  # 脚本配置信号
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.current_script = None
        self.dependency_analyzer = DependencyAnalyzer()
        self.package_manager = PackageManager()
        self.script_executor = ScriptExecutor()
        self.current_dependencies = None
        self.current_execution_id = None
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 创建内容组件
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        
        # 标题
        title_label = QLabel("📄 脚本详情")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; padding: 5px;")
        content_layout.addWidget(title_label)
        
        # 基本信息组
        self.create_basic_info_group(content_layout)
        
        # 依赖信息组
        self.create_dependencies_group(content_layout)
        
        # 脚本预览组
        self.create_preview_group(content_layout)
        
        # 操作按钮组
        self.create_action_buttons(content_layout)
        
        # 添加弹性空间
        content_layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
        
        # 初始状态显示空信息
        self.show_empty_state()
        
    def create_basic_info_group(self, parent_layout):
        """创建基本信息组"""
        group = QGroupBox("📋 基本信息")
        layout = QVBoxLayout(group)
        
        # 脚本名称
        self.name_label = QLabel("脚本名称: -")
        self.name_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(self.name_label)
        
        # 文件路径
        self.path_label = QLabel("文件路径: -")
        self.path_label.setWordWrap(True)
        layout.addWidget(self.path_label)
        
        # 文件大小
        self.size_label = QLabel("文件大小: -")
        layout.addWidget(self.size_label)
        
        # 修改时间
        self.modified_label = QLabel("修改时间: -")
        layout.addWidget(self.modified_label)
        
        # 脚本描述
        self.description_label = QLabel("描述: -")
        self.description_label.setWordWrap(True)
        layout.addWidget(self.description_label)
        
        # 状态
        self.status_label = QLabel("状态: -")
        layout.addWidget(self.status_label)
        
        parent_layout.addWidget(group)
        
    def create_dependencies_group(self, parent_layout):
        """创建依赖信息组"""
        group = QGroupBox("📦 依赖信息")
        layout = QVBoxLayout(group)
        
        # 依赖状态
        self.deps_status_label = QLabel("依赖状态: 未检查")
        layout.addWidget(self.deps_status_label)
        
        # 依赖列表
        self.deps_text = QTextEdit()
        self.deps_text.setMaximumHeight(100)
        self.deps_text.setPlaceholderText("点击'检查依赖'按钮分析脚本依赖...")
        self.deps_text.setReadOnly(True)
        layout.addWidget(self.deps_text)
        
        # 依赖操作按钮
        deps_button_layout = QHBoxLayout()
        
        self.check_deps_btn = QPushButton("🔍 检查依赖")
        self.check_deps_btn.clicked.connect(self.check_dependencies)
        self.check_deps_btn.setEnabled(False)
        deps_button_layout.addWidget(self.check_deps_btn)
        
        self.install_deps_btn = QPushButton("📥 安装依赖")
        self.install_deps_btn.clicked.connect(self.install_dependencies)
        self.install_deps_btn.setEnabled(False)
        deps_button_layout.addWidget(self.install_deps_btn)
        
        deps_button_layout.addStretch()
        layout.addLayout(deps_button_layout)
        
        parent_layout.addWidget(group)
        
    def create_preview_group(self, parent_layout):
        """创建脚本预览组"""
        group = QGroupBox("👁️ 脚本预览")
        layout = QVBoxLayout(group)
        
        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(200)
        self.preview_text.setPlaceholderText("选择脚本后显示预览...")
        self.preview_text.setReadOnly(True)
        
        # 设置等宽字体
        font = QFont("Consolas", 10)
        font.setStyleHint(QFont.StyleHint.Monospace)
        self.preview_text.setFont(font)
        
        layout.addWidget(self.preview_text)
        
        parent_layout.addWidget(group)
        
    def create_action_buttons(self, parent_layout):
        """创建操作按钮组"""
        group = QGroupBox("🔧 操作")
        layout = QVBoxLayout(group)
        
        # 第一行按钮
        button_layout1 = QHBoxLayout()
        
        self.config_btn = QPushButton("⚙️ 配置脚本")
        self.config_btn.clicked.connect(self.configure_script)
        self.config_btn.setEnabled(False)
        button_layout1.addWidget(self.config_btn)
        
        self.execute_btn = QPushButton("▶️ 执行脚本")
        self.execute_btn.clicked.connect(self.execute_script)
        self.execute_btn.setEnabled(False)
        self.execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        button_layout1.addWidget(self.execute_btn)
        
        layout.addLayout(button_layout1)
        
        # 第二行按钮
        button_layout2 = QHBoxLayout()
        
        self.edit_btn = QPushButton("✏️ 编辑脚本")
        self.edit_btn.clicked.connect(self.edit_script)
        self.edit_btn.setEnabled(False)
        button_layout2.addWidget(self.edit_btn)
        
        self.open_folder_btn = QPushButton("📁 打开文件夹")
        self.open_folder_btn.clicked.connect(self.open_folder)
        self.open_folder_btn.setEnabled(False)
        button_layout2.addWidget(self.open_folder_btn)
        
        layout.addLayout(button_layout2)
        
        parent_layout.addWidget(group)
        
    def load_script(self, script_path):
        """加载脚本信息"""
        try:
            self.current_script = script_path
            script_path = Path(script_path)
            
            if not script_path.exists():
                self.show_error_state("脚本文件不存在")
                return
                
            self.logger.info(f"加载脚本详情: {script_path}")
            
            # 更新基本信息
            self.name_label.setText(f"脚本名称: {script_path.name}")
            self.path_label.setText(f"文件路径: {script_path}")
            
            # 文件大小
            size = script_path.stat().st_size
            size_str = self.format_file_size(size)
            self.size_label.setText(f"文件大小: {size_str}")
            
            # 修改时间
            import datetime
            mtime = script_path.stat().st_mtime
            modified_time = datetime.datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
            self.modified_label.setText(f"修改时间: {modified_time}")
            
            # 读取脚本内容
            try:
                with open(script_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 提取描述
                description = self.extract_description(content)
                self.description_label.setText(f"描述: {description}")
                
                # 显示预览（前50行）
                lines = content.split('\n')
                preview_lines = lines[:50]
                if len(lines) > 50:
                    preview_lines.append(f"\n... (还有 {len(lines) - 50} 行)")
                self.preview_text.setPlainText('\n'.join(preview_lines))
                
            except Exception as e:
                self.logger.error(f"读取脚本内容失败: {e}")
                self.description_label.setText("描述: 无法读取文件内容")
                self.preview_text.setPlainText(f"无法读取文件内容: {e}")
                
            # 更新状态
            self.status_label.setText("状态: ✅ 已加载")
            
            # 启用按钮
            self.enable_buttons(True)
            
            # 重置依赖信息
            self.deps_status_label.setText("依赖状态: 未检查")
            self.deps_text.clear()
            
        except Exception as e:
            self.logger.error(f"加载脚本失败: {e}")
            self.show_error_state(f"加载失败: {e}")
            
    def extract_description(self, content):
        """提取脚本描述"""
        lines = content.split('\n')
        
        # 查找文档字符串
        for i, line in enumerate(lines[:20]):  # 只检查前20行
            line = line.strip()
            if line.startswith('"""') or line.startswith("'''"):
                quote = '"""' if line.startswith('"""') else "'''"
                if line.count(quote) == 2:
                    # 单行文档字符串
                    return line.strip(quote).strip()
                else:
                    # 多行文档字符串
                    desc_lines = [line.strip(quote)]
                    for j in range(i + 1, min(i + 10, len(lines))):
                        next_line = lines[j].strip()
                        if quote in next_line:
                            desc_lines.append(next_line.split(quote)[0])
                            break
                        desc_lines.append(next_line)
                    return ' '.join(desc_lines).strip()
                    
        # 查找注释
        for line in lines[:10]:
            line = line.strip()
            if line.startswith('#') and not line.startswith('#!/'):
                return line.lstrip('#').strip()
                
        return "无描述"
        
    def format_file_size(self, size):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
        
    def show_empty_state(self):
        """显示空状态"""
        self.name_label.setText("脚本名称: -")
        self.path_label.setText("文件路径: -")
        self.size_label.setText("文件大小: -")
        self.modified_label.setText("修改时间: -")
        self.description_label.setText("描述: -")
        self.status_label.setText("状态: 请选择脚本")
        self.preview_text.clear()
        self.deps_text.clear()
        self.deps_status_label.setText("依赖状态: -")
        self.enable_buttons(False)
        
    def show_error_state(self, error_msg):
        """显示错误状态"""
        self.status_label.setText(f"状态: ❌ {error_msg}")
        self.enable_buttons(False)
        
    def enable_buttons(self, enabled):
        """启用/禁用按钮"""
        self.config_btn.setEnabled(enabled)
        self.execute_btn.setEnabled(enabled)
        self.edit_btn.setEnabled(enabled)
        self.open_folder_btn.setEnabled(enabled)
        self.check_deps_btn.setEnabled(enabled)
        
    def check_dependencies(self):
        """检查依赖"""
        if not self.current_script:
            return

        self.logger.info("检查脚本依赖")
        self.deps_status_label.setText("依赖状态: 🔍 检查中...")
        self.deps_text.clear()

        try:
            # 分析依赖
            result = self.dependency_analyzer.analyze_script(self.current_script)

            if result['success']:
                self.current_dependencies = result
                self._display_dependencies(result)

                # 更新状态
                missing_count = len(result['missing_packages'])
                if missing_count == 0:
                    self.deps_status_label.setText("依赖状态: ✅ 所有依赖已安装")
                    self.install_deps_btn.setEnabled(False)
                else:
                    self.deps_status_label.setText(f"依赖状态: ⚠️ 缺少 {missing_count} 个依赖")
                    self.install_deps_btn.setEnabled(True)
            else:
                self.deps_text.setPlainText(f"依赖分析失败: {result['error']}")
                self.deps_status_label.setText("依赖状态: ❌ 分析失败")
                self.install_deps_btn.setEnabled(False)

        except Exception as e:
            self.logger.error(f"检查依赖失败: {e}")
            self.deps_text.setPlainText(f"检查依赖时发生错误: {e}")
            self.deps_status_label.setText("依赖状态: ❌ 检查失败")
            self.install_deps_btn.setEnabled(False)

    def _display_dependencies(self, result):
        """显示依赖信息"""
        output = []

        # 标准库
        if result['standard_library']:
            output.append("📚 标准库:")
            for lib in result['standard_library']:
                output.append(f"  ✅ {lib}")
            output.append("")

        # 第三方库
        if result['third_party']:
            output.append("📦 第三方库:")
            for lib in result['third_party']:
                status = result['third_party_status'].get(lib, {})
                if status.get('installed', False):
                    version = status.get('version', '未知版本')
                    output.append(f"  ✅ {lib} ({version})")
                else:
                    output.append(f"  ❌ {lib} (未安装)")
            output.append("")

        # 本地模块
        if result['local_modules']:
            output.append("📁 本地模块:")
            for lib in result['local_modules']:
                output.append(f"  📄 {lib}")
            output.append("")

        # 缺失的包
        if result['missing_packages']:
            output.append("⚠️ 需要安装的包:")
            suggestions = self.dependency_analyzer.get_dependency_suggestions(result['missing_packages'])
            for pkg in result['missing_packages']:
                pip_name = suggestions.get(pkg, pkg)
                if pip_name != pkg:
                    output.append(f"  📥 {pkg} (pip install {pip_name})")
                else:
                    output.append(f"  📥 {pkg} (pip install {pkg})")
            output.append("")

        # 统计信息
        output.append("📊 统计信息:")
        output.append(f"  总导入数: {result['total_imports']}")
        output.append(f"  标准库: {len(result['standard_library'])}")
        output.append(f"  第三方库: {len(result['third_party'])}")
        output.append(f"  本地模块: {len(result['local_modules'])}")
        output.append(f"  缺失包: {len(result['missing_packages'])}")

        self.deps_text.setPlainText("\n".join(output))

    def install_dependencies(self):
        """安装依赖"""
        if not self.current_dependencies or not self.current_dependencies.get('missing_packages'):
            QMessageBox.information(self, "提示", "没有需要安装的依赖包")
            return

        missing_packages = self.current_dependencies['missing_packages']
        suggestions = self.dependency_analyzer.get_dependency_suggestions(missing_packages)

        # 确认安装
        package_list = []
        for pkg in missing_packages:
            pip_name = suggestions.get(pkg, pkg)
            package_list.append(f"• {pkg} (pip install {pip_name})")

        message = f"将要安装以下 {len(missing_packages)} 个依赖包:\n\n"
        message += "\n".join(package_list)
        message += "\n\n是否继续安装？"

        reply = QMessageBox.question(
            self, "确认安装", message,
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.Yes
        )

        if reply == QMessageBox.StandardButton.Yes:
            self._install_packages_async(missing_packages, suggestions)

    def _install_packages_async(self, packages, suggestions):
        """异步安装包"""
        from PyQt6.QtCore import QThread, pyqtSignal

        class InstallThread(QThread):
            progress = pyqtSignal(str)
            finished = pyqtSignal(dict)

            def __init__(self, package_manager, packages, suggestions):
                super().__init__()
                self.package_manager = package_manager
                self.packages = packages
                self.suggestions = suggestions

            def run(self):
                # 转换包名
                pip_packages = []
                for pkg in self.packages:
                    pip_name = self.suggestions.get(pkg, pkg)
                    pip_packages.append(pip_name)

                # 安装包
                result = self.package_manager.install_packages(
                    pip_packages,
                    progress_callback=self.progress.emit
                )
                self.finished.emit(result)

        # 禁用按钮
        self.install_deps_btn.setEnabled(False)
        self.deps_status_label.setText("依赖状态: 📥 正在安装...")

        # 创建并启动安装线程
        self.install_thread = InstallThread(self.package_manager, packages, suggestions)
        self.install_thread.progress.connect(self._on_install_progress)
        self.install_thread.finished.connect(self._on_install_finished)
        self.install_thread.start()

    def _on_install_progress(self, message):
        """安装进度回调"""
        # 在日志中显示进度（如果有日志组件的话）
        self.logger.info(f"安装进度: {message}")

    def _on_install_finished(self, result):
        """安装完成回调"""
        self.install_deps_btn.setEnabled(True)

        if result['success'] == result['total']:
            self.deps_status_label.setText("依赖状态: ✅ 安装完成")
            QMessageBox.information(
                self, "安装完成",
                f"成功安装了 {result['success']} 个依赖包"
            )
            # 重新检查依赖
            self.check_dependencies()
        else:
            self.deps_status_label.setText(f"依赖状态: ⚠️ 部分安装失败")
            failed_packages = result.get('failed_packages', [])
            message = f"安装完成，但有 {result['failed']} 个包安装失败:\n"
            message += "\n".join(f"• {pkg}" for pkg in failed_packages)
            QMessageBox.warning(self, "安装部分失败", message)

    def configure_script(self):
        """配置脚本"""
        if self.current_script:
            self.logger.info(f"配置脚本: {self.current_script}")
            self.script_configured.emit(self.current_script)
            # TODO: 打开配置对话框
            QMessageBox.information(self, "提示", "脚本配置功能正在开发中...")
            
    def execute_script(self):
        """执行脚本"""
        if not self.current_script:
            return

        # 检查是否已有脚本在运行
        if self.current_execution_id and self.script_executor.is_running(self.current_execution_id):
            reply = QMessageBox.question(
                self, "确认停止",
                "当前有脚本正在运行，是否停止当前脚本并执行新脚本？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                self.script_executor.stop_execution(self.current_execution_id)
            else:
                return

        self.logger.info(f"执行脚本: {self.current_script}")

        # 更新按钮状态
        self.execute_btn.setText("⏹️ 停止执行")
        self.execute_btn.clicked.disconnect()
        self.execute_btn.clicked.connect(self.stop_script)

        # 发送执行信号
        self.script_executed.emit(self.current_script)

        # 执行配置
        config = {
            'timeout': 300,  # 5分钟超时
            'working_directory': str(Path(self.current_script).parent),
            'environment_variables': {},
            'arguments': []
        }

        try:
            # 开始执行
            self.current_execution_id = self.script_executor.execute_script(
                self.current_script,
                config=config,
                output_callback=self._on_script_output,
                finished_callback=self._on_script_finished
            )

            # 更新状态
            self.status_label.setText("状态: ▶️ 正在执行")

        except Exception as e:
            self.logger.error(f"执行脚本失败: {e}")
            QMessageBox.critical(self, "执行失败", f"执行脚本失败:\n{e}")
            self._reset_execute_button()

    def stop_script(self):
        """停止脚本执行"""
        if self.current_execution_id:
            success = self.script_executor.stop_execution(self.current_execution_id)
            if success:
                self.logger.info("用户停止了脚本执行")
            self._reset_execute_button()

    def _on_script_output(self, line, output_type):
        """脚本输出回调"""
        # 这里可以将输出发送到日志组件
        # 暂时记录到日志文件
        if output_type == "stdout":
            self.logger.info(f"[脚本输出] {line}")
        elif output_type == "stderr":
            self.logger.error(f"[脚本错误] {line}")
        elif output_type == "info":
            self.logger.info(f"[执行信息] {line}")
        elif output_type == "error":
            self.logger.error(f"[执行错误] {line}")
        elif output_type == "success":
            self.logger.info(f"[执行成功] {line}")

    def _on_script_finished(self, result):
        """脚本执行完成回调"""
        self.current_execution_id = None
        self._reset_execute_button()

        if result['success']:
            self.status_label.setText(f"状态: ✅ 执行完成 (耗时: {result['execution_time']:.2f}秒)")
            QMessageBox.information(
                self, "执行完成",
                f"脚本执行成功！\n耗时: {result['execution_time']:.2f}秒"
            )
        else:
            self.status_label.setText("状态: ❌ 执行失败")
            error_msg = result.get('error', '未知错误')
            if result.get('return_code') is not None:
                error_msg = f"返回码: {result['return_code']}"
            QMessageBox.critical(
                self, "执行失败",
                f"脚本执行失败！\n错误: {error_msg}"
            )

    def _reset_execute_button(self):
        """重置执行按钮"""
        self.execute_btn.setText("▶️ 执行脚本")
        self.execute_btn.clicked.disconnect()
        self.execute_btn.clicked.connect(self.execute_script)
            
    def edit_script(self):
        """编辑脚本"""
        if self.current_script:
            self.logger.info(f"编辑脚本: {self.current_script}")
            # TODO: 打开外部编辑器或内置编辑器
            import subprocess
            try:
                subprocess.run(['notepad.exe', self.current_script])
            except Exception as e:
                QMessageBox.warning(self, "警告", f"无法打开编辑器: {e}")
                
    def open_folder(self):
        """打开文件夹"""
        if self.current_script:
            folder_path = Path(self.current_script).parent
            self.logger.info(f"打开文件夹: {folder_path}")
            import subprocess
            try:
                subprocess.run(['explorer', str(folder_path)])
            except Exception as e:
                QMessageBox.warning(self, "警告", f"无法打开文件夹: {e}")
