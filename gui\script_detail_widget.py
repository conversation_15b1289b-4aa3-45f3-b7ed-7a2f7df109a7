#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本详情组件
显示选中脚本的详细信息和操作按钮
"""

import os
from pathlib import Path
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QTextEdit, QGroupBox, QScrollArea,
    QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from utils.logger import get_logger


class ScriptDetailWidget(QWidget):
    """脚本详情组件"""
    
    # 信号定义
    script_executed = pyqtSignal(str)  # 脚本执行信号
    script_configured = pyqtSignal(str)  # 脚本配置信号
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.current_script = None
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 创建内容组件
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        
        # 标题
        title_label = QLabel("📄 脚本详情")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; padding: 5px;")
        content_layout.addWidget(title_label)
        
        # 基本信息组
        self.create_basic_info_group(content_layout)
        
        # 依赖信息组
        self.create_dependencies_group(content_layout)
        
        # 脚本预览组
        self.create_preview_group(content_layout)
        
        # 操作按钮组
        self.create_action_buttons(content_layout)
        
        # 添加弹性空间
        content_layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
        
        # 初始状态显示空信息
        self.show_empty_state()
        
    def create_basic_info_group(self, parent_layout):
        """创建基本信息组"""
        group = QGroupBox("📋 基本信息")
        layout = QVBoxLayout(group)
        
        # 脚本名称
        self.name_label = QLabel("脚本名称: -")
        self.name_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(self.name_label)
        
        # 文件路径
        self.path_label = QLabel("文件路径: -")
        self.path_label.setWordWrap(True)
        layout.addWidget(self.path_label)
        
        # 文件大小
        self.size_label = QLabel("文件大小: -")
        layout.addWidget(self.size_label)
        
        # 修改时间
        self.modified_label = QLabel("修改时间: -")
        layout.addWidget(self.modified_label)
        
        # 脚本描述
        self.description_label = QLabel("描述: -")
        self.description_label.setWordWrap(True)
        layout.addWidget(self.description_label)
        
        # 状态
        self.status_label = QLabel("状态: -")
        layout.addWidget(self.status_label)
        
        parent_layout.addWidget(group)
        
    def create_dependencies_group(self, parent_layout):
        """创建依赖信息组"""
        group = QGroupBox("📦 依赖信息")
        layout = QVBoxLayout(group)
        
        # 依赖状态
        self.deps_status_label = QLabel("依赖状态: 未检查")
        layout.addWidget(self.deps_status_label)
        
        # 依赖列表
        self.deps_text = QTextEdit()
        self.deps_text.setMaximumHeight(100)
        self.deps_text.setPlaceholderText("点击'检查依赖'按钮分析脚本依赖...")
        self.deps_text.setReadOnly(True)
        layout.addWidget(self.deps_text)
        
        # 依赖操作按钮
        deps_button_layout = QHBoxLayout()
        
        self.check_deps_btn = QPushButton("🔍 检查依赖")
        self.check_deps_btn.clicked.connect(self.check_dependencies)
        self.check_deps_btn.setEnabled(False)
        deps_button_layout.addWidget(self.check_deps_btn)
        
        self.install_deps_btn = QPushButton("📥 安装依赖")
        self.install_deps_btn.clicked.connect(self.install_dependencies)
        self.install_deps_btn.setEnabled(False)
        deps_button_layout.addWidget(self.install_deps_btn)
        
        deps_button_layout.addStretch()
        layout.addLayout(deps_button_layout)
        
        parent_layout.addWidget(group)
        
    def create_preview_group(self, parent_layout):
        """创建脚本预览组"""
        group = QGroupBox("👁️ 脚本预览")
        layout = QVBoxLayout(group)
        
        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(200)
        self.preview_text.setPlaceholderText("选择脚本后显示预览...")
        self.preview_text.setReadOnly(True)
        
        # 设置等宽字体
        font = QFont("Consolas", 10)
        font.setStyleHint(QFont.StyleHint.Monospace)
        self.preview_text.setFont(font)
        
        layout.addWidget(self.preview_text)
        
        parent_layout.addWidget(group)
        
    def create_action_buttons(self, parent_layout):
        """创建操作按钮组"""
        group = QGroupBox("🔧 操作")
        layout = QVBoxLayout(group)
        
        # 第一行按钮
        button_layout1 = QHBoxLayout()
        
        self.config_btn = QPushButton("⚙️ 配置脚本")
        self.config_btn.clicked.connect(self.configure_script)
        self.config_btn.setEnabled(False)
        button_layout1.addWidget(self.config_btn)
        
        self.execute_btn = QPushButton("▶️ 执行脚本")
        self.execute_btn.clicked.connect(self.execute_script)
        self.execute_btn.setEnabled(False)
        self.execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        button_layout1.addWidget(self.execute_btn)
        
        layout.addLayout(button_layout1)
        
        # 第二行按钮
        button_layout2 = QHBoxLayout()
        
        self.edit_btn = QPushButton("✏️ 编辑脚本")
        self.edit_btn.clicked.connect(self.edit_script)
        self.edit_btn.setEnabled(False)
        button_layout2.addWidget(self.edit_btn)
        
        self.open_folder_btn = QPushButton("📁 打开文件夹")
        self.open_folder_btn.clicked.connect(self.open_folder)
        self.open_folder_btn.setEnabled(False)
        button_layout2.addWidget(self.open_folder_btn)
        
        layout.addLayout(button_layout2)
        
        parent_layout.addWidget(group)
        
    def load_script(self, script_path):
        """加载脚本信息"""
        try:
            self.current_script = script_path
            script_path = Path(script_path)
            
            if not script_path.exists():
                self.show_error_state("脚本文件不存在")
                return
                
            self.logger.info(f"加载脚本详情: {script_path}")
            
            # 更新基本信息
            self.name_label.setText(f"脚本名称: {script_path.name}")
            self.path_label.setText(f"文件路径: {script_path}")
            
            # 文件大小
            size = script_path.stat().st_size
            size_str = self.format_file_size(size)
            self.size_label.setText(f"文件大小: {size_str}")
            
            # 修改时间
            import datetime
            mtime = script_path.stat().st_mtime
            modified_time = datetime.datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
            self.modified_label.setText(f"修改时间: {modified_time}")
            
            # 读取脚本内容
            try:
                with open(script_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 提取描述
                description = self.extract_description(content)
                self.description_label.setText(f"描述: {description}")
                
                # 显示预览（前50行）
                lines = content.split('\n')
                preview_lines = lines[:50]
                if len(lines) > 50:
                    preview_lines.append(f"\n... (还有 {len(lines) - 50} 行)")
                self.preview_text.setPlainText('\n'.join(preview_lines))
                
            except Exception as e:
                self.logger.error(f"读取脚本内容失败: {e}")
                self.description_label.setText("描述: 无法读取文件内容")
                self.preview_text.setPlainText(f"无法读取文件内容: {e}")
                
            # 更新状态
            self.status_label.setText("状态: ✅ 已加载")
            
            # 启用按钮
            self.enable_buttons(True)
            
            # 重置依赖信息
            self.deps_status_label.setText("依赖状态: 未检查")
            self.deps_text.clear()
            
        except Exception as e:
            self.logger.error(f"加载脚本失败: {e}")
            self.show_error_state(f"加载失败: {e}")
            
    def extract_description(self, content):
        """提取脚本描述"""
        lines = content.split('\n')
        
        # 查找文档字符串
        for i, line in enumerate(lines[:20]):  # 只检查前20行
            line = line.strip()
            if line.startswith('"""') or line.startswith("'''"):
                quote = '"""' if line.startswith('"""') else "'''"
                if line.count(quote) == 2:
                    # 单行文档字符串
                    return line.strip(quote).strip()
                else:
                    # 多行文档字符串
                    desc_lines = [line.strip(quote)]
                    for j in range(i + 1, min(i + 10, len(lines))):
                        next_line = lines[j].strip()
                        if quote in next_line:
                            desc_lines.append(next_line.split(quote)[0])
                            break
                        desc_lines.append(next_line)
                    return ' '.join(desc_lines).strip()
                    
        # 查找注释
        for line in lines[:10]:
            line = line.strip()
            if line.startswith('#') and not line.startswith('#!/'):
                return line.lstrip('#').strip()
                
        return "无描述"
        
    def format_file_size(self, size):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
        
    def show_empty_state(self):
        """显示空状态"""
        self.name_label.setText("脚本名称: -")
        self.path_label.setText("文件路径: -")
        self.size_label.setText("文件大小: -")
        self.modified_label.setText("修改时间: -")
        self.description_label.setText("描述: -")
        self.status_label.setText("状态: 请选择脚本")
        self.preview_text.clear()
        self.deps_text.clear()
        self.deps_status_label.setText("依赖状态: -")
        self.enable_buttons(False)
        
    def show_error_state(self, error_msg):
        """显示错误状态"""
        self.status_label.setText(f"状态: ❌ {error_msg}")
        self.enable_buttons(False)
        
    def enable_buttons(self, enabled):
        """启用/禁用按钮"""
        self.config_btn.setEnabled(enabled)
        self.execute_btn.setEnabled(enabled)
        self.edit_btn.setEnabled(enabled)
        self.open_folder_btn.setEnabled(enabled)
        self.check_deps_btn.setEnabled(enabled)
        
    def check_dependencies(self):
        """检查依赖"""
        if not self.current_script:
            return
            
        self.logger.info("检查脚本依赖")
        self.deps_status_label.setText("依赖状态: 🔍 检查中...")
        
        # TODO: 实现依赖检查功能
        self.deps_text.setPlainText("依赖检查功能正在开发中...")
        self.deps_status_label.setText("依赖状态: ⏳ 待实现")
        self.install_deps_btn.setEnabled(True)
        
    def install_dependencies(self):
        """安装依赖"""
        self.logger.info("安装脚本依赖")
        # TODO: 实现依赖安装功能
        QMessageBox.information(self, "提示", "依赖安装功能正在开发中...")
        
    def configure_script(self):
        """配置脚本"""
        if self.current_script:
            self.logger.info(f"配置脚本: {self.current_script}")
            self.script_configured.emit(self.current_script)
            # TODO: 打开配置对话框
            QMessageBox.information(self, "提示", "脚本配置功能正在开发中...")
            
    def execute_script(self):
        """执行脚本"""
        if self.current_script:
            self.logger.info(f"执行脚本: {self.current_script}")
            self.script_executed.emit(self.current_script)
            # TODO: 实现脚本执行功能
            QMessageBox.information(self, "提示", "脚本执行功能正在开发中...")
            
    def edit_script(self):
        """编辑脚本"""
        if self.current_script:
            self.logger.info(f"编辑脚本: {self.current_script}")
            # TODO: 打开外部编辑器或内置编辑器
            import subprocess
            try:
                subprocess.run(['notepad.exe', self.current_script])
            except Exception as e:
                QMessageBox.warning(self, "警告", f"无法打开编辑器: {e}")
                
    def open_folder(self):
        """打开文件夹"""
        if self.current_script:
            folder_path = Path(self.current_script).parent
            self.logger.info(f"打开文件夹: {folder_path}")
            import subprocess
            try:
                subprocess.run(['explorer', str(folder_path)])
            except Exception as e:
                QMessageBox.warning(self, "警告", f"无法打开文件夹: {e}")
