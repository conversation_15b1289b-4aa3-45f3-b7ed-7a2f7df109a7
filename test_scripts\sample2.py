#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例脚本2 - 文件操作示例
演示文件读写和目录操作
"""

import os
import json
import datetime
from pathlib import Path

def create_sample_data():
    """创建示例数据"""
    data = {
        "name": "PyScriptRunner测试",
        "version": "1.0.0",
        "created": datetime.datetime.now().isoformat(),
        "items": [
            {"id": 1, "name": "项目A", "status": "进行中"},
            {"id": 2, "name": "项目B", "status": "已完成"},
            {"id": 3, "name": "项目C", "status": "计划中"},
        ]
    }
    return data

def main():
    """主函数"""
    print("=== 示例脚本2 开始执行 ===")
    
    # 创建输出目录
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    print(f"创建输出目录: {output_dir.absolute()}")
    
    # 生成示例数据
    data = create_sample_data()
    print("生成示例数据完成")
    
    # 保存为JSON文件
    json_file = output_dir / "sample_data.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    print(f"数据已保存到: {json_file}")
    
    # 保存为文本文件
    txt_file = output_dir / "sample_report.txt"
    with open(txt_file, 'w', encoding='utf-8') as f:
        f.write("PyScriptRunner 测试报告\n")
        f.write("=" * 30 + "\n\n")
        f.write(f"生成时间: {datetime.datetime.now()}\n")
        f.write(f"项目总数: {len(data['items'])}\n\n")
        
        for item in data['items']:
            f.write(f"项目 {item['id']}: {item['name']} - {item['status']}\n")
            
    print(f"报告已保存到: {txt_file}")
    
    # 列出生成的文件
    print(f"\n生成的文件:")
    for file in output_dir.iterdir():
        if file.is_file():
            size = file.stat().st_size
            print(f"  {file.name} ({size} 字节)")
    
    print("\n=== 脚本执行完成 ===")

if __name__ == "__main__":
    main()
