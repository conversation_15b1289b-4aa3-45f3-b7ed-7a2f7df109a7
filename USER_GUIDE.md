# 📖 PyScriptRunner 用户使用指南

## 🚀 快速开始

### 1. 启动程序

**方式一：直接运行**
```bash
python main.py
```

**方式二：使用批处理文件**
```bash
run.bat
```

### 2. 导入脚本

1. 点击工具栏的 **"导入"** 按钮
2. 选择包含Python脚本的文件夹
3. 程序会自动扫描并导入所有 `.py` 文件
4. 导入的脚本会保存到数据库，下次启动时自动加载

### 3. 管理脚本

- **查看脚本**：在左侧列表中点击脚本名称
- **脚本详情**：右侧面板显示脚本的详细信息
- **移除脚本**：右键点击脚本 → 选择"从列表移除"

## 🔧 核心功能

### 📦 依赖管理

#### 检查依赖
1. 选择一个脚本
2. 在右侧详情面板点击 **"🔍 检查依赖"** 按钮
3. 程序会自动分析脚本中的import语句
4. 显示依赖分类：
   - **📚 标准库**：Python内置模块
   - **📦 第三方库**：需要安装的外部包
   - **📁 本地模块**：项目内的模块
   - **⚠️ 需要安装的包**：缺失的依赖

#### 安装依赖
1. 检查依赖后，如果有缺失的包
2. 点击 **"📥 安装依赖"** 按钮
3. 确认要安装的包列表
4. 程序会自动使用pip安装缺失的依赖
5. 安装完成后可以重新检查依赖状态

### ▶️ 脚本执行

#### 执行脚本
1. 选择要执行的脚本
2. 点击 **"▶️ 执行脚本"** 按钮
3. 脚本开始执行，按钮变为 **"⏹️ 停止执行"**
4. 执行过程中可以在日志面板查看实时输出
5. 执行完成后显示结果和耗时

#### 停止执行
- 在脚本执行过程中点击 **"⏹️ 停止执行"** 按钮
- 程序会尝试优雅地终止脚本进程

### 📋 日志系统

#### 查看日志
- 下方的日志面板实时显示程序运行信息
- 不同级别的日志用不同颜色显示：
  - **白色**：普通信息 (INFO)
  - **橙色**：警告信息 (WARNING)  
  - **红色**：错误信息 (ERROR)
  - **绿色**：成功信息 (SUCCESS)

#### 日志操作
- **级别过滤**：选择要显示的日志级别
- **自动滚动**：勾选后自动滚动到最新日志
- **清除日志**：点击 🗑️ 按钮清空日志显示
- **导出日志**：点击 💾 按钮保存日志到文件

## 🎯 使用场景

### 场景1：运行数据处理脚本

1. **导入脚本**：导入包含数据处理脚本的文件夹
2. **检查依赖**：确保pandas、numpy等库已安装
3. **执行脚本**：运行脚本处理数据
4. **查看结果**：在日志中查看处理结果

### 场景2：测试新的Python库

1. **创建测试脚本**：编写使用新库的测试代码
2. **导入到PyScriptRunner**：将脚本导入程序
3. **自动安装依赖**：让程序自动安装所需的库
4. **快速测试**：执行脚本验证功能

### 场景3：批量管理脚本

1. **导入项目文件夹**：一次性导入整个项目的脚本
2. **分类查看**：按文件夹分组查看脚本
3. **逐个测试**：依次执行各个脚本
4. **问题排查**：通过日志快速定位问题

## 💡 使用技巧

### 1. 脚本编写建议

**添加文档字符串**
```python
"""
这是一个数据处理脚本
用于清洗和分析用户数据
"""
```

**使用标准的import语句**
```python
import pandas as pd
import numpy as np
from pathlib import Path
```

**添加执行信息**
```python
if __name__ == "__main__":
    print("开始处理数据...")
    # 处理逻辑
    print("数据处理完成")
```

### 2. 依赖管理技巧

- **使用常见包名**：程序能自动识别常见的包名映射
- **避免相对导入**：使用绝对导入便于依赖分析
- **分离依赖**：将第三方库导入放在文件顶部

### 3. 执行优化

- **设置合理的超时时间**：避免长时间运行的脚本卡死
- **处理异常**：在脚本中添加异常处理
- **输出进度信息**：使用print输出执行进度

## ⚠️ 注意事项

### 安全提醒

1. **谨慎执行未知脚本**：确保脚本来源可信
2. **检查脚本内容**：执行前查看脚本预览
3. **备份重要数据**：执行可能修改文件的脚本前备份

### 性能建议

1. **避免无限循环**：确保脚本能正常结束
2. **合理使用资源**：避免占用过多内存或CPU
3. **及时清理**：定期清理不需要的脚本

### 兼容性说明

1. **Python版本**：建议使用Python 3.8+
2. **依赖冲突**：注意不同脚本间的依赖版本冲突
3. **路径问题**：使用绝对路径或相对于脚本的路径

## 🔧 故障排除

### 常见问题

**Q: 导入脚本后列表为空？**
A: 检查文件夹中是否包含.py文件，确保文件编码为UTF-8

**Q: 依赖检查失败？**
A: 检查脚本语法是否正确，确保没有语法错误

**Q: 依赖安装失败？**
A: 检查网络连接，确保能访问PyPI镜像源

**Q: 脚本执行卡住？**
A: 点击停止按钮终止执行，检查脚本是否有无限循环

**Q: 日志显示乱码？**
A: 确保脚本文件使用UTF-8编码保存

### 获取帮助

1. **查看日志**：详细的错误信息会记录在日志中
2. **检查文件**：确认脚本文件路径和权限
3. **重启程序**：遇到异常时可以尝试重启程序
4. **查看文档**：参考开发文档了解更多技术细节

## 📞 技术支持

- **项目主页**：[GitHub Repository]
- **问题反馈**：[Issues]
- **使用交流**：[Discussions]

---

🎉 **祝您使用愉快！** 如果遇到问题或有改进建议，欢迎反馈！
