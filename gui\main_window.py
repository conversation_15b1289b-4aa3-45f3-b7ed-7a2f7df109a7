#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口模块
PyScriptRunner的主界面
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QSplitter, QMenuBar, QToolBar, QStatusBar,
    QFileDialog, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon, QKeySequence, QAction

from .script_list_widget import ScriptListWidget
from .script_detail_widget import ScriptDetailWidget
from .log_widget import LogWidget
from utils.logger import get_logger


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    script_selected = pyqtSignal(str)  # 脚本选择信号
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("PyScriptRunner - Python脚本执行器")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 创建左侧脚本列表
        self.script_list = ScriptListWidget()
        splitter.addWidget(self.script_list)
        
        # 创建右侧分割器
        right_splitter = QSplitter(Qt.Orientation.Vertical)
        splitter.addWidget(right_splitter)
        
        # 创建脚本详情面板
        self.script_detail = ScriptDetailWidget()
        right_splitter.addWidget(self.script_detail)
        
        # 创建日志面板
        self.log_widget = LogWidget()
        right_splitter.addWidget(self.log_widget)
        
        # 设置分割器比例
        splitter.setSizes([300, 900])
        right_splitter.setSizes([400, 400])
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_tool_bar()
        
        # 创建状态栏
        self.create_status_bar()
        
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        # 导入脚本
        import_action = QAction('导入脚本(&I)', self)
        import_action.setShortcut(QKeySequence.StandardKey.Open)
        import_action.setStatusTip('导入Python脚本文件')
        import_action.triggered.connect(self.import_scripts)
        file_menu.addAction(import_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.setStatusTip('退出程序')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu('编辑(&E)')
        
        # 刷新
        refresh_action = QAction('刷新(&R)', self)
        refresh_action.setShortcut(QKeySequence.StandardKey.Refresh)
        refresh_action.setStatusTip('刷新脚本列表')
        refresh_action.triggered.connect(self.refresh_scripts)
        edit_menu.addAction(refresh_action)
        
        # 视图菜单
        view_menu = menubar.addMenu('视图(&V)')
        
        # 工具菜单
        tools_menu = menubar.addMenu('工具(&T)')
        
        # 设置
        settings_action = QAction('设置(&S)', self)
        settings_action.setStatusTip('打开设置对话框')
        settings_action.triggered.connect(self.open_settings)
        tools_menu.addAction(settings_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        # 关于
        about_action = QAction('关于(&A)', self)
        about_action.setStatusTip('关于PyScriptRunner')
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def create_tool_bar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('主工具栏')
        toolbar.setMovable(False)
        
        # 导入脚本按钮
        import_action = QAction('导入', self)
        import_action.setStatusTip('导入Python脚本')
        import_action.triggered.connect(self.import_scripts)
        toolbar.addAction(import_action)
        
        # 刷新按钮
        refresh_action = QAction('刷新', self)
        refresh_action.setStatusTip('刷新脚本列表')
        refresh_action.triggered.connect(self.refresh_scripts)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        # 设置按钮
        settings_action = QAction('设置', self)
        settings_action.setStatusTip('打开设置')
        settings_action.triggered.connect(self.open_settings)
        toolbar.addAction(settings_action)
        
        # 帮助按钮
        help_action = QAction('帮助', self)
        help_action.setStatusTip('显示帮助信息')
        help_action.triggered.connect(self.show_help)
        toolbar.addAction(help_action)
        
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage('就绪')
        
    def setup_connections(self):
        """设置信号连接"""
        # 脚本列表选择信号
        self.script_list.script_selected.connect(self.on_script_selected)
        
    def import_scripts(self):
        """导入脚本"""
        try:
            # 选择文件夹
            folder = QFileDialog.getExistingDirectory(
                self, 
                '选择包含Python脚本的文件夹',
                '',
                QFileDialog.Option.ShowDirsOnly
            )
            
            if folder:
                self.logger.info(f"开始导入脚本文件夹: {folder}")
                self.status_bar.showMessage(f'正在导入脚本: {folder}')
                
                # 调用脚本列表的导入方法
                self.script_list.import_scripts_from_folder(folder)
                
                self.status_bar.showMessage('脚本导入完成')
                self.logger.info("脚本导入完成")
                
        except Exception as e:
            self.logger.error(f"导入脚本失败: {e}")
            QMessageBox.critical(self, '错误', f'导入脚本失败:\n{e}')
            
    def refresh_scripts(self):
        """刷新脚本列表"""
        try:
            self.logger.info("刷新脚本列表")
            self.script_list.refresh()
            self.status_bar.showMessage('脚本列表已刷新')
        except Exception as e:
            self.logger.error(f"刷新脚本列表失败: {e}")
            
    def on_script_selected(self, script_path):
        """处理脚本选择事件"""
        self.logger.info(f"选择脚本: {script_path}")
        self.script_detail.load_script(script_path)
        
    def open_settings(self):
        """打开设置对话框"""
        self.logger.info("打开设置对话框")
        # TODO: 实现设置对话框
        QMessageBox.information(self, '提示', '设置功能正在开发中...')
        
    def show_about(self):
        """显示关于对话框"""
        about_text = """
        <h3>PyScriptRunner</h3>
        <p>版本: 1.0.0</p>
        <p>Python一体化脚本执行器</p>
        <p>提供图形化界面来管理、配置和执行Python脚本</p>
        <p><a href="https://github.com/your-username/PyScriptRunner">项目主页</a></p>
        """
        QMessageBox.about(self, '关于 PyScriptRunner', about_text)
        
    def show_help(self):
        """显示帮助信息"""
        help_text = """
        <h3>使用帮助</h3>
        <p><b>1. 导入脚本:</b> 点击"导入"按钮选择包含Python脚本的文件夹</p>
        <p><b>2. 选择脚本:</b> 在左侧列表中点击要执行的脚本</p>
        <p><b>3. 配置脚本:</b> 在右侧面板中配置脚本参数</p>
        <p><b>4. 执行脚本:</b> 点击"执行"按钮运行脚本</p>
        <p><b>5. 查看日志:</b> 在下方日志面板查看执行结果</p>
        """
        QMessageBox.information(self, '帮助', help_text)
