#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
包管理器
处理Python包的安装、卸载和管理
"""

import sys
import subprocess
import threading
import time
from pathlib import Path
from typing import List, Dict, Optional, Callable, Any
from utils.logger import get_logger


class PackageManager:
    """包管理器"""
    
    def __init__(self, python_executable: str = None):
        self.logger = get_logger(__name__)
        self.python_executable = python_executable or sys.executable
        self.pip_executable = self._get_pip_executable()
        
    def _get_pip_executable(self) -> str:
        """获取pip可执行文件路径"""
        # 优先使用python -m pip的方式
        return f'"{self.python_executable}" -m pip'
        
    def install_package(self, package_name: str, 
                       version: str = None,
                       index_url: str = None,
                       timeout: int = 300,
                       progress_callback: Callable[[str], None] = None) -> Dict[str, Any]:
        """
        安装Python包
        
        Args:
            package_name: 包名
            version: 版本号（可选）
            index_url: PyPI镜像地址（可选）
            timeout: 超时时间（秒）
            progress_callback: 进度回调函数
            
        Returns:
            Dict: 安装结果
        """
        try:
            self.logger.info(f"开始安装包: {package_name}")
            
            # 构建安装命令
            if version:
                package_spec = f"{package_name}=={version}"
            else:
                package_spec = package_name
                
            cmd = f"{self.pip_executable} install {package_spec}"
            
            # 添加镜像源
            if index_url:
                cmd += f" -i {index_url}"
            else:
                # 使用默认的清华镜像源
                cmd += " -i https://pypi.tuna.tsinghua.edu.cn/simple/"
                
            # 添加其他参数
            cmd += " --trusted-host pypi.tuna.tsinghua.edu.cn"
            cmd += f" --timeout {timeout}"
            
            if progress_callback:
                progress_callback(f"执行命令: {cmd}")
                
            # 执行安装
            result = self._run_command(cmd, timeout, progress_callback)
            
            if result['success']:
                self.logger.info(f"包安装成功: {package_name}")
                return {
                    'success': True,
                    'package': package_name,
                    'message': f"成功安装 {package_name}",
                    'output': result['output']
                }
            else:
                self.logger.error(f"包安装失败: {package_name}, 错误: {result['error']}")
                return {
                    'success': False,
                    'package': package_name,
                    'message': f"安装 {package_name} 失败",
                    'error': result['error'],
                    'output': result['output']
                }
                
        except Exception as e:
            self.logger.error(f"安装包时发生异常: {e}")
            return {
                'success': False,
                'package': package_name,
                'message': f"安装 {package_name} 时发生异常",
                'error': str(e)
            }
            
    def install_packages(self, packages: List[str],
                        progress_callback: Callable[[str], None] = None) -> Dict[str, Any]:
        """
        批量安装包
        
        Args:
            packages: 包名列表
            progress_callback: 进度回调函数
            
        Returns:
            Dict: 安装结果统计
        """
        results = {
            'total': len(packages),
            'success': 0,
            'failed': 0,
            'details': [],
            'failed_packages': []
        }
        
        for i, package in enumerate(packages):
            if progress_callback:
                progress_callback(f"正在安装 {package} ({i+1}/{len(packages)})")
                
            result = self.install_package(package, progress_callback=progress_callback)
            results['details'].append(result)
            
            if result['success']:
                results['success'] += 1
            else:
                results['failed'] += 1
                results['failed_packages'].append(package)
                
        return results
        
    def uninstall_package(self, package_name: str,
                         progress_callback: Callable[[str], None] = None) -> Dict[str, Any]:
        """卸载包"""
        try:
            self.logger.info(f"开始卸载包: {package_name}")
            
            cmd = f"{self.pip_executable} uninstall {package_name} -y"
            
            if progress_callback:
                progress_callback(f"执行命令: {cmd}")
                
            result = self._run_command(cmd, 60, progress_callback)
            
            if result['success']:
                self.logger.info(f"包卸载成功: {package_name}")
                return {
                    'success': True,
                    'package': package_name,
                    'message': f"成功卸载 {package_name}"
                }
            else:
                return {
                    'success': False,
                    'package': package_name,
                    'message': f"卸载 {package_name} 失败",
                    'error': result['error']
                }
                
        except Exception as e:
            self.logger.error(f"卸载包时发生异常: {e}")
            return {
                'success': False,
                'package': package_name,
                'message': f"卸载 {package_name} 时发生异常",
                'error': str(e)
            }
            
    def list_installed_packages(self) -> List[Dict[str, str]]:
        """列出已安装的包"""
        try:
            cmd = f"{self.pip_executable} list --format=json"
            result = self._run_command(cmd, 30)
            
            if result['success']:
                import json
                packages = json.loads(result['output'])
                return packages
            else:
                self.logger.error(f"获取包列表失败: {result['error']}")
                return []
                
        except Exception as e:
            self.logger.error(f"获取包列表时发生异常: {e}")
            return []
            
    def check_package_installed(self, package_name: str) -> bool:
        """检查包是否已安装"""
        try:
            cmd = f"{self.pip_executable} show {package_name}"
            result = self._run_command(cmd, 10)
            return result['success']
        except:
            return False
            
    def get_package_info(self, package_name: str) -> Optional[Dict[str, str]]:
        """获取包信息"""
        try:
            cmd = f"{self.pip_executable} show {package_name}"
            result = self._run_command(cmd, 10)
            
            if result['success']:
                info = {}
                for line in result['output'].split('\n'):
                    if ':' in line:
                        key, value = line.split(':', 1)
                        info[key.strip()] = value.strip()
                return info
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"获取包信息失败: {e}")
            return None
            
    def upgrade_package(self, package_name: str,
                       progress_callback: Callable[[str], None] = None) -> Dict[str, Any]:
        """升级包"""
        try:
            self.logger.info(f"开始升级包: {package_name}")
            
            cmd = f"{self.pip_executable} install --upgrade {package_name}"
            cmd += " -i https://pypi.tuna.tsinghua.edu.cn/simple/"
            cmd += " --trusted-host pypi.tuna.tsinghua.edu.cn"
            
            if progress_callback:
                progress_callback(f"执行命令: {cmd}")
                
            result = self._run_command(cmd, 300, progress_callback)
            
            if result['success']:
                self.logger.info(f"包升级成功: {package_name}")
                return {
                    'success': True,
                    'package': package_name,
                    'message': f"成功升级 {package_name}"
                }
            else:
                return {
                    'success': False,
                    'package': package_name,
                    'message': f"升级 {package_name} 失败",
                    'error': result['error']
                }
                
        except Exception as e:
            self.logger.error(f"升级包时发生异常: {e}")
            return {
                'success': False,
                'package': package_name,
                'message': f"升级 {package_name} 时发生异常",
                'error': str(e)
            }
            
    def _run_command(self, cmd: str, timeout: int = 60,
                    progress_callback: Callable[[str], None] = None) -> Dict[str, Any]:
        """执行命令"""
        try:
            self.logger.debug(f"执行命令: {cmd}")
            
            # 使用subprocess执行命令
            process = subprocess.Popen(
                cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            output_lines = []
            error_lines = []
            
            # 实时读取输出
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    line = output.strip()
                    output_lines.append(line)
                    if progress_callback:
                        progress_callback(line)
                        
            # 读取错误输出
            stderr_output = process.stderr.read()
            if stderr_output:
                error_lines.append(stderr_output.strip())
                
            # 等待进程结束
            return_code = process.wait(timeout=timeout)
            
            output_text = '\n'.join(output_lines)
            error_text = '\n'.join(error_lines)
            
            return {
                'success': return_code == 0,
                'return_code': return_code,
                'output': output_text,
                'error': error_text
            }
            
        except subprocess.TimeoutExpired:
            process.kill()
            return {
                'success': False,
                'return_code': -1,
                'output': '',
                'error': f'命令执行超时 ({timeout}秒)'
            }
        except Exception as e:
            return {
                'success': False,
                'return_code': -1,
                'output': '',
                'error': str(e)
            }
