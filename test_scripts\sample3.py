#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例脚本3 - 第三方库依赖示例
演示使用第三方库的脚本（用于测试依赖检测）
"""

# 标准库导入
import os
import sys
import json
import datetime
from pathlib import Path

# 第三方库导入（注释掉避免运行错误）
# import requests
# import pandas as pd
# import numpy as np
# from PIL import Image

def simulate_requests():
    """模拟requests库的使用"""
    print("模拟HTTP请求...")
    # 这里模拟requests.get()的行为
    mock_response = {
        "status_code": 200,
        "data": {
            "message": "这是模拟的API响应",
            "timestamp": datetime.datetime.now().isoformat(),
            "items": ["item1", "item2", "item3"]
        }
    }
    return mock_response

def simulate_pandas():
    """模拟pandas的使用"""
    print("模拟数据分析...")
    # 模拟DataFrame操作
    data = [
        {"name": "<PERSON>", "age": 25, "city": "北京"},
        {"name": "<PERSON>", "age": 30, "city": "上海"},
        {"name": "<PERSON>", "age": 35, "city": "广州"},
    ]
    
    print("数据预览:")
    for i, row in enumerate(data):
        print(f"  {i}: {row}")
        
    return data

def simulate_numpy():
    """模拟numpy的使用"""
    print("模拟数值计算...")
    # 模拟numpy数组操作
    data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    
    # 模拟统计计算
    mean_val = sum(data) / len(data)
    max_val = max(data)
    min_val = min(data)
    
    print(f"数组: {data}")
    print(f"平均值: {mean_val}")
    print(f"最大值: {max_val}")
    print(f"最小值: {min_val}")
    
    return {"mean": mean_val, "max": max_val, "min": min_val}

def main():
    """主函数"""
    print("=== 示例脚本3 开始执行 ===")
    print("注意: 此脚本包含第三方库导入（已注释），用于测试依赖检测功能")
    
    # 模拟网络请求
    print("\n1. 模拟网络请求")
    response = simulate_requests()
    print(f"响应状态: {response['status_code']}")
    print(f"响应数据: {response['data']['message']}")
    
    # 模拟数据分析
    print("\n2. 模拟数据分析")
    df_data = simulate_pandas()
    print(f"处理了 {len(df_data)} 行数据")
    
    # 模拟数值计算
    print("\n3. 模拟数值计算")
    stats = simulate_numpy()
    print(f"计算结果: {stats}")
    
    # 保存结果
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    result = {
        "script": "sample3.py",
        "execution_time": datetime.datetime.now().isoformat(),
        "api_response": response,
        "data_analysis": df_data,
        "statistics": stats,
        "dependencies": [
            "requests",  # HTTP库
            "pandas",    # 数据分析库
            "numpy",     # 数值计算库
            "Pillow"     # 图像处理库
        ]
    }
    
    result_file = output_dir / "sample3_result.json"
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
        
    print(f"\n结果已保存到: {result_file}")
    print("\n=== 脚本执行完成 ===")

if __name__ == "__main__":
    main()
