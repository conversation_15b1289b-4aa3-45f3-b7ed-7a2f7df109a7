# 🐍 PyScriptRunner - Python一体化脚本执行器

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![PyQt6](https://img.shields.io/badge/PyQt6-6.0+-green.svg)](https://pypi.org/project/PyQt6/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

一个集成Python解释器的可执行程序，提供图形化界面来管理、配置和执行Python脚本，无需用户预装Python环境。

## ✨ 核心特性

- 🚀 **零配置运行** - 自带Python解释器，开箱即用
- 🧠 **智能依赖管理** - 自动检测和安装Python包
- 🎨 **可视化操作** - 直观的图形界面，降低使用门槛
- 📦 **脚本管理** - 统一管理和执行多个Python脚本
- ⚙️ **灵活配置** - 丰富的脚本执行配置选项
- 📊 **实时监控** - 实时日志显示和执行状态监控

## 🎯 适用场景

- **Python初学者** - 无需配置Python环境
- **运维人员** - 批量管理和执行自动化脚本
- **数据分析师** - 快速运行数据处理脚本
- **开发者** - 测试和调试Python脚本

## 🚀 快速开始

### 环境要求

- Windows 10/11 (64位)
- 4GB+ 内存
- 500MB+ 磁盘空间

### 开发环境搭建

1. **克隆项目**
```bash
git clone https://github.com/your-username/PyScriptRunner.git
cd PyScriptRunner
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **运行程序**
```bash
python main.py
```

## 📋 功能概览

### 🔧 脚本管理
- 📁 文件夹扫描和脚本导入
- 🏷️ 脚本分类和标签管理
- 🔍 智能搜索和过滤
- ⭐ 收藏和置顶功能

### ⚙️ 配置管理
- 📝 脚本基本信息配置
- 🔧 运行参数和环境变量设置
- ⏱️ 执行控制和超时设置
- 🔄 前置/后置操作配置

### 📦 依赖管理
- 🔍 智能依赖检测（AST分析）
- 📥 自动依赖安装
- 🌐 镜像源和代理配置
- 🔒 虚拟环境隔离

### ▶️ 脚本执行
- 🎯 单脚本/批量执行
- ⏰ 定时任务支持
- 📊 实时监控和日志
- 🔄 执行队列管理

## 🏗️ 项目结构

```
PyScriptRunner/
├── main.py                     # 程序入口
├── core/                       # 核心功能模块
│   ├── script_manager.py       # 脚本管理器
│   ├── dependency_analyzer.py  # 依赖分析器
│   ├── package_manager.py      # 包管理器
│   ├── script_executor.py      # 脚本执行器
│   └── database.py             # 数据库操作
├── gui/                        # 图形界面模块
│   ├── main_window.py          # 主窗口
│   ├── script_list_widget.py   # 脚本列表组件
│   ├── script_detail_widget.py # 脚本详情组件
│   ├── config_dialog.py        # 配置对话框
│   └── log_widget.py           # 日志显示组件
├── utils/                      # 工具模块
│   ├── file_utils.py           # 文件操作工具
│   └── logger.py               # 日志工具
├── resources/                  # 资源文件
│   ├── icons/                  # 图标文件
│   └── styles/                 # 样式文件
├── data/                       # 数据目录
│   ├── scripts.db              # 脚本数据库
│   └── logs/                   # 日志文件
└── tests/                      # 测试文件
```

## 🎨 界面预览

### 主界面
```
┌─────────────────────────────────────────────────────────┐
│  PyScriptRunner - Python脚本执行器                      │
├─────────────────────────────────────────────────────────┤
│  [导入] [刷新] [设置] [帮助]                             │
├──────────────┬──────────────────────────────────────────┤
│  脚本列表    │  脚本详情和执行日志                        │
│  📁 项目A    │  ┌─────────────────────────────────────┐  │
│    📄 a.py   │  │ 脚本: test.py                       │  │
│    📄 b.py   │  │ 状态: ✅ 就绪                       │  │
│  📁 项目B    │  │ 依赖: pandas, numpy                 │  │
│    📄 c.py   │  └─────────────────────────────────────┘  │
│              │  [配置] [执行] [编辑]                     │
│              │  ──────────────────────────────────────  │
│              │  📋 执行日志                              │
│              │  > 开始执行脚本...                        │
│              │  > 检查依赖: pandas ✅                    │
│              │  > 脚本执行完成 ✅                        │
└──────────────┴──────────────────────────────────────────┘
```

## 🔄 使用流程

1. **导入脚本** → 选择包含Python脚本的文件夹
2. **浏览管理** → 在列表中查看和管理脚本
3. **配置脚本** → 设置运行参数和环境变量
4. **检查依赖** → 自动分析并安装依赖包
5. **执行脚本** → 一键运行并查看实时日志
6. **查看结果** → 检查执行历史和统计信息

## 🛠️ 技术栈

- **GUI框架**: PyQt6
- **Python版本**: 3.8+
- **数据库**: SQLite
- **打包工具**: PyInstaller
- **依赖分析**: AST + importlib

## 📈 开发计划

- [x] 项目初始化和基础框架
- [ ] 脚本管理功能
- [ ] 依赖分析和管理
- [ ] 脚本执行引擎
- [ ] 配置管理界面
- [ ] 日志和监控系统
- [ ] 打包和分发

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目主页: [GitHub Repository](https://github.com/your-username/PyScriptRunner)
- 问题反馈: [Issues](https://github.com/your-username/PyScriptRunner/issues)

---

⭐ 如果这个项目对你有帮助，请给个Star支持一下！
