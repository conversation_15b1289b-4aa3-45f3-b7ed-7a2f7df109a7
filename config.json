{"app": {"name": "PyScript<PERSON><PERSON>ner", "version": "1.0.0", "description": "Python一体化脚本执行器", "author": "PyScriptRunner Team"}, "settings": {"max_log_lines": 1000, "auto_scroll_logs": true, "check_dependencies_on_import": true, "auto_install_dependencies": false, "default_python_interpreter": "python", "script_timeout": 300, "max_concurrent_scripts": 3}, "ui": {"window_width": 1200, "window_height": 800, "splitter_left_width": 300, "splitter_right_width": 900, "theme": "default", "font_family": "Consolas", "font_size": 10}, "paths": {"data_dir": "data", "logs_dir": "data/logs", "environments_dir": "data/environments", "scripts_db": "data/scripts.db", "temp_dir": "data/temp"}, "pip": {"index_url": "https://pypi.org/simple/", "trusted_hosts": [], "timeout": 60, "retries": 3}}