#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖分析器
使用AST分析Python脚本的依赖关系
"""

import ast
import sys
import importlib.util
from pathlib import Path
from typing import List, Dict, Set, Optional, Any
from utils.logger import get_logger


class DependencyAnalyzer:
    """依赖分析器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.standard_modules = self._get_standard_modules()
        
    def analyze_script(self, script_path: str) -> Dict[str, Any]:
        """
        分析脚本依赖
        
        Args:
            script_path: 脚本文件路径
            
        Returns:
            Dict: 依赖分析结果
        """
        try:
            script_path = Path(script_path)
            if not script_path.exists():
                raise FileNotFoundError(f"脚本文件不存在: {script_path}")
                
            self.logger.info(f"分析脚本依赖: {script_path}")
            
            # 读取脚本内容
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 解析AST
            try:
                tree = ast.parse(content, filename=str(script_path))
            except SyntaxError as e:
                return {
                    'success': False,
                    'error': f'语法错误: {e}',
                    'dependencies': []
                }
                
            # 提取导入信息
            imports = self._extract_imports(tree)
            
            # 分类依赖
            categorized = self._categorize_dependencies(imports)
            
            # 检查安装状态
            third_party_status = self._check_installation_status(categorized['third_party'])
            
            result = {
                'success': True,
                'script_path': str(script_path),
                'total_imports': len(imports),
                'standard_library': categorized['standard'],
                'third_party': categorized['third_party'],
                'local_modules': categorized['local'],
                'unknown': categorized['unknown'],
                'third_party_status': third_party_status,
                'missing_packages': [pkg for pkg, status in third_party_status.items() if not status['installed']],
                'analysis_time': None  # 可以添加时间戳
            }
            
            self.logger.info(f"依赖分析完成: 标准库{len(categorized['standard'])}, 第三方{len(categorized['third_party'])}, 本地{len(categorized['local'])}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"分析脚本依赖失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'dependencies': []
            }
            
    def _extract_imports(self, tree: ast.AST) -> List[Dict[str, Any]]:
        """从AST中提取导入信息"""
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append({
                        'type': 'import',
                        'module': alias.name,
                        'name': alias.asname or alias.name,
                        'line': node.lineno,
                        'level': 0
                    })
                    
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ''
                level = node.level
                
                for alias in node.names:
                    imports.append({
                        'type': 'from_import',
                        'module': module,
                        'name': alias.name,
                        'asname': alias.asname,
                        'line': node.lineno,
                        'level': level
                    })
                    
        return imports
        
    def _categorize_dependencies(self, imports: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """分类依赖"""
        categories = {
            'standard': set(),
            'third_party': set(),
            'local': set(),
            'unknown': set()
        }
        
        for imp in imports:
            module_name = imp['module']
            
            # 处理相对导入
            if imp['level'] > 0:
                categories['local'].add(module_name or '.')
                continue
                
            # 获取顶级模块名
            top_module = module_name.split('.')[0] if module_name else ''
            
            if not top_module:
                continue
                
            # 分类
            if self._is_standard_library(top_module):
                categories['standard'].add(top_module)
            elif self._is_local_module(top_module):
                categories['local'].add(top_module)
            else:
                # 可能是第三方库
                categories['third_party'].add(top_module)
                
        # 转换为列表并排序
        return {k: sorted(list(v)) for k, v in categories.items()}
        
    def _is_standard_library(self, module_name: str) -> bool:
        """判断是否为标准库模块"""
        return module_name in self.standard_modules
        
    def _is_local_module(self, module_name: str) -> bool:
        """判断是否为本地模块"""
        # 简单判断：如果模块名包含当前目录的文件，则认为是本地模块
        # 这里可以根据实际需要改进
        return False  # 暂时简化处理
        
    def _check_installation_status(self, packages: List[str]) -> Dict[str, Dict[str, Any]]:
        """检查第三方包的安装状态"""
        status = {}
        
        for package in packages:
            try:
                # 尝试导入模块
                spec = importlib.util.find_spec(package)
                if spec is not None:
                    # 尝试获取版本信息
                    version = self._get_package_version(package)
                    status[package] = {
                        'installed': True,
                        'version': version,
                        'location': spec.origin if spec.origin else 'built-in'
                    }
                else:
                    status[package] = {
                        'installed': False,
                        'version': None,
                        'location': None
                    }
                    
            except Exception as e:
                status[package] = {
                    'installed': False,
                    'version': None,
                    'location': None,
                    'error': str(e)
                }
                
        return status
        
    def _get_package_version(self, package_name: str) -> Optional[str]:
        """获取包版本"""
        try:
            # 尝试多种方式获取版本
            module = importlib.import_module(package_name)
            
            # 常见的版本属性
            for attr in ['__version__', 'version', 'VERSION']:
                if hasattr(module, attr):
                    version = getattr(module, attr)
                    if isinstance(version, str):
                        return version
                    elif hasattr(version, '__str__'):
                        return str(version)
                        
            # 尝试使用importlib.metadata (Python 3.8+)
            try:
                import importlib.metadata
                return importlib.metadata.version(package_name)
            except:
                pass
                
            # 尝试使用pkg_resources
            try:
                import pkg_resources
                return pkg_resources.get_distribution(package_name).version
            except:
                pass
                
        except Exception:
            pass
            
        return None
        
    def _get_standard_modules(self) -> Set[str]:
        """获取标准库模块列表"""
        # Python标准库模块列表（主要的）
        standard_modules = {
            # 内置模块
            'builtins', 'sys', 'os', 'io', 'time', 'datetime', 'math', 'random',
            'json', 'csv', 'xml', 'html', 'urllib', 'http', 'email', 'base64',
            'hashlib', 'hmac', 'secrets', 'uuid', 'pickle', 'copy', 'pprint',
            
            # 文件和路径
            'pathlib', 'glob', 'fnmatch', 'tempfile', 'shutil', 'stat',
            
            # 数据结构
            'collections', 'array', 'heapq', 'bisect', 'weakref',
            
            # 函数式编程
            'itertools', 'functools', 'operator',
            
            # 字符串和正则
            'string', 're', 'difflib', 'textwrap', 'unicodedata',
            
            # 数据压缩
            'zlib', 'gzip', 'bz2', 'lzma', 'zipfile', 'tarfile',
            
            # 数据库
            'sqlite3', 'dbm',
            
            # 网络
            'socket', 'ssl', 'select', 'selectors', 'asyncio',
            
            # 并发
            'threading', 'multiprocessing', 'concurrent', 'subprocess', 'queue',
            
            # 配置和日志
            'configparser', 'logging', 'argparse', 'getopt',
            
            # 测试
            'unittest', 'doctest', 'test',
            
            # 调试和性能
            'pdb', 'profile', 'cProfile', 'timeit', 'trace', 'traceback',
            
            # 系统相关
            'platform', 'ctypes', 'mmap', 'resource', 'gc', 'atexit',
            
            # 其他常用
            'warnings', 'contextlib', 'abc', 'numbers', 'decimal', 'fractions',
            'statistics', 'enum', 'types', 'typing', 'dataclasses'
        }
        
        # 添加sys.builtin_module_names中的模块
        standard_modules.update(sys.builtin_module_names)
        
        return standard_modules
        
    def get_dependency_suggestions(self, missing_packages: List[str]) -> Dict[str, str]:
        """获取依赖安装建议"""
        suggestions = {}
        
        # 常见包名映射
        package_mapping = {
            'PIL': 'Pillow',
            'cv2': 'opencv-python',
            'sklearn': 'scikit-learn',
            'yaml': 'PyYAML',
            'bs4': 'beautifulsoup4',
            'requests_oauthlib': 'requests-oauthlib',
            'jwt': 'PyJWT',
            'dateutil': 'python-dateutil',
            'serial': 'pyserial',
            'psutil': 'psutil',
            'lxml': 'lxml',
            'openpyxl': 'openpyxl',
            'xlrd': 'xlrd',
            'matplotlib': 'matplotlib',
            'numpy': 'numpy',
            'pandas': 'pandas',
            'scipy': 'scipy',
            'flask': 'Flask',
            'django': 'Django',
            'fastapi': 'fastapi',
            'sqlalchemy': 'SQLAlchemy',
            'pymongo': 'pymongo',
            'redis': 'redis',
            'celery': 'celery'
        }
        
        for package in missing_packages:
            pip_name = package_mapping.get(package, package)
            suggestions[package] = pip_name
            
        return suggestions
