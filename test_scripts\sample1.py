#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例脚本1 - 简单的数据处理
这是一个用于测试PyScriptRunner的示例脚本
"""

import os
import sys
import time
import random

def main():
    """主函数"""
    print("=== 示例脚本1 开始执行 ===")
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 模拟数据处理
    print("\n开始处理数据...")
    for i in range(5):
        time.sleep(1)
        progress = (i + 1) * 20
        print(f"处理进度: {progress}%")
        
    # 生成一些随机数据
    data = [random.randint(1, 100) for _ in range(10)]
    print(f"\n生成的数据: {data}")
    
    # 计算统计信息
    total = sum(data)
    average = total / len(data)
    max_val = max(data)
    min_val = min(data)
    
    print(f"\n统计结果:")
    print(f"总和: {total}")
    print(f"平均值: {average:.2f}")
    print(f"最大值: {max_val}")
    print(f"最小值: {min_val}")
    
    print("\n=== 脚本执行完成 ===")

if __name__ == "__main__":
    main()
