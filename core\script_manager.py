#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本管理器
统一管理脚本的导入、存储、查询等操作
"""

import os
import datetime
from pathlib import Path
from typing import List, Dict, Optional, Any
from .database import ScriptDatabase
from utils.logger import get_logger


class ScriptManager:
    """脚本管理器"""
    
    def __init__(self, db_path: str = "data/scripts.db"):
        self.db = ScriptDatabase(db_path)
        self.logger = get_logger(__name__)
        
    def import_scripts_from_folder(self, folder_path: str) -> Dict[str, Any]:
        """
        从文件夹导入脚本
        
        Args:
            folder_path: 文件夹路径
            
        Returns:
            Dict: 导入结果统计
        """
        try:
            folder_path = Path(folder_path)
            if not folder_path.exists():
                raise FileNotFoundError(f"文件夹不存在: {folder_path}")
                
            self.logger.info(f"开始导入脚本文件夹: {folder_path}")
            
            # 递归查找Python文件
            python_files = list(folder_path.rglob("*.py"))
            
            if not python_files:
                return {
                    'success': False,
                    'message': '在选择的文件夹中没有找到Python文件',
                    'total': 0,
                    'imported': 0,
                    'updated': 0,
                    'skipped': 0
                }
                
            # 统计信息
            stats = {
                'total': len(python_files),
                'imported': 0,
                'updated': 0,
                'skipped': 0,
                'errors': []
            }
            
            # 处理每个Python文件
            for py_file in python_files:
                try:
                    result = self.import_single_script(py_file)
                    if result['action'] == 'imported':
                        stats['imported'] += 1
                    elif result['action'] == 'updated':
                        stats['updated'] += 1
                    else:
                        stats['skipped'] += 1
                        
                except Exception as e:
                    stats['errors'].append(f"{py_file.name}: {str(e)}")
                    self.logger.error(f"导入脚本失败 {py_file}: {e}")
                    
            # 清理不存在的脚本
            removed_count = self.db.cleanup_missing_scripts()
            
            self.logger.info(f"导入完成: 总计{stats['total']}, 新增{stats['imported']}, 更新{stats['updated']}, 跳过{stats['skipped']}, 清理{removed_count}")
            
            return {
                'success': True,
                'message': f"导入完成: 新增{stats['imported']}个, 更新{stats['updated']}个",
                **stats,
                'removed': removed_count
            }
            
        except Exception as e:
            self.logger.error(f"导入脚本文件夹失败: {e}")
            return {
                'success': False,
                'message': f"导入失败: {str(e)}",
                'total': 0,
                'imported': 0,
                'updated': 0,
                'skipped': 0
            }
            
    def import_single_script(self, script_path: Path) -> Dict[str, Any]:
        """
        导入单个脚本
        
        Args:
            script_path: 脚本文件路径
            
        Returns:
            Dict: 导入结果
        """
        try:
            if not script_path.exists() or script_path.suffix != '.py':
                return {'action': 'skipped', 'reason': '文件不存在或不是Python文件'}
                
            # 提取脚本信息
            script_info = self.extract_script_info(script_path)
            
            # 检查是否已存在
            existing = self.db.get_script_by_path(script_info['path'])
            
            if existing:
                # 检查文件是否有更新
                if existing['modified_time'] < script_info['modified']:
                    self.db.update_script(existing['id'], script_info)
                    return {'action': 'updated', 'script_id': existing['id']}
                else:
                    return {'action': 'skipped', 'reason': '文件未修改'}
            else:
                # 添加新脚本
                script_id = self.db.add_script(script_info)
                return {'action': 'imported', 'script_id': script_id}
                
        except Exception as e:
            self.logger.error(f"导入单个脚本失败 {script_path}: {e}")
            raise
            
    def extract_script_info(self, script_path: Path) -> Dict[str, Any]:
        """
        提取脚本信息
        
        Args:
            script_path: 脚本文件路径
            
        Returns:
            Dict: 脚本信息
        """
        info = {
            'path': str(script_path.absolute()),
            'name': script_path.name,
            'folder': script_path.parent.name,
            'size': script_path.stat().st_size,
            'modified': script_path.stat().st_mtime,
            'description': '',
            'dependencies': [],
            'config': {},
            'tags': []
        }
        
        # 尝试读取脚本内容获取更多信息
        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 提取描述
            info['description'] = self.extract_description(content)
            
            # 提取基础依赖信息（简单的import分析）
            info['dependencies'] = self.extract_basic_dependencies(content)
            
            # 提取标签（从注释中）
            info['tags'] = self.extract_tags(content)
            
        except Exception as e:
            self.logger.warning(f"读取脚本内容失败 {script_path}: {e}")
            
        return info
        
    def extract_description(self, content: str) -> str:
        """从脚本内容提取描述"""
        lines = content.split('\n')
        
        # 查找文档字符串
        for i, line in enumerate(lines[:20]):
            line = line.strip()
            if line.startswith('"""') or line.startswith("'''"):
                quote = '"""' if line.startswith('"""') else "'''"
                if line.count(quote) == 2:
                    # 单行文档字符串
                    return line.strip(quote).strip()
                else:
                    # 多行文档字符串
                    desc_lines = [line.strip(quote)]
                    for j in range(i + 1, min(i + 10, len(lines))):
                        next_line = lines[j].strip()
                        if quote in next_line:
                            desc_lines.append(next_line.split(quote)[0])
                            break
                        desc_lines.append(next_line)
                    return ' '.join(desc_lines).strip()
                    
        # 查找注释
        for line in lines[:10]:
            line = line.strip()
            if line.startswith('#') and not line.startswith('#!/'):
                desc = line.lstrip('#').strip()
                if len(desc) > 5:  # 过滤太短的注释
                    return desc
                    
        return "无描述"
        
    def extract_basic_dependencies(self, content: str) -> List[str]:
        """提取基础依赖信息（简单分析）"""
        dependencies = set()
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # 匹配 import xxx
            if line.startswith('import ') and not line.startswith('import os') and not line.startswith('import sys'):
                parts = line.split()
                if len(parts) >= 2:
                    module = parts[1].split('.')[0]
                    if not self.is_standard_library(module):
                        dependencies.add(module)
                        
            # 匹配 from xxx import
            elif line.startswith('from ') and ' import ' in line:
                parts = line.split()
                if len(parts) >= 4:
                    module = parts[1].split('.')[0]
                    if not self.is_standard_library(module):
                        dependencies.add(module)
                        
        return list(dependencies)
        
    def extract_tags(self, content: str) -> List[str]:
        """从脚本内容提取标签"""
        tags = set()
        lines = content.split('\n')
        
        # 从注释中查找标签
        for line in lines[:20]:
            line = line.strip().lower()
            if 'tag' in line or '标签' in line:
                # 简单的标签提取逻辑
                if 'data' in line or '数据' in line:
                    tags.add('数据处理')
                if 'web' in line or '网络' in line:
                    tags.add('网络')
                if 'file' in line or '文件' in line:
                    tags.add('文件操作')
                if 'test' in line or '测试' in line:
                    tags.add('测试')
                    
        return list(tags)
        
    def is_standard_library(self, module_name: str) -> bool:
        """判断是否为标准库模块"""
        standard_modules = {
            'os', 'sys', 'json', 'datetime', 'time', 'random', 'math',
            'pathlib', 'collections', 'itertools', 'functools', 'operator',
            'string', 're', 'urllib', 'http', 'email', 'html', 'xml',
            'sqlite3', 'csv', 'configparser', 'logging', 'unittest',
            'threading', 'multiprocessing', 'subprocess', 'socket',
            'hashlib', 'base64', 'pickle', 'copy', 'weakref'
        }
        return module_name in standard_modules
        
    def get_all_scripts(self) -> List[Dict[str, Any]]:
        """获取所有脚本"""
        return self.db.get_all_scripts()
        
    def get_script_by_path(self, path: str) -> Optional[Dict[str, Any]]:
        """根据路径获取脚本"""
        return self.db.get_script_by_path(path)
        
    def remove_script(self, script_path: str):
        """移除脚本"""
        self.db.remove_script_by_path(script_path)
        
    def update_script_config(self, script_path: str, config: Dict[str, Any]):
        """更新脚本配置"""
        script = self.db.get_script_by_path(script_path)
        if script:
            self.db.update_script_config(script['id'], config)
            
    def search_scripts(self, query: str) -> List[Dict[str, Any]]:
        """搜索脚本"""
        all_scripts = self.get_all_scripts()
        query = query.lower()
        
        results = []
        for script in all_scripts:
            # 搜索名称、描述、标签
            if (query in script['name'].lower() or 
                query in script.get('description', '').lower() or
                any(query in tag.lower() for tag in script.get('tags', []))):
                results.append(script)
                
        return results
        
    def get_scripts_by_folder(self, folder: str) -> List[Dict[str, Any]]:
        """获取指定文件夹的脚本"""
        all_scripts = self.get_all_scripts()
        return [s for s in all_scripts if s['folder'] == folder]
        
    def get_favorite_scripts(self) -> List[Dict[str, Any]]:
        """获取收藏的脚本"""
        all_scripts = self.get_all_scripts()
        return [s for s in all_scripts if s.get('favorite', 0) == 1]
        
    def toggle_favorite(self, script_path: str):
        """切换脚本收藏状态"""
        script = self.db.get_script_by_path(script_path)
        if script:
            new_favorite = 1 if script.get('favorite', 0) == 0 else 0
            # 这里需要在数据库中添加更新收藏状态的方法
            # 暂时通过更新配置来实现
            config = script.get('config', {})
            config['favorite'] = new_favorite
            self.db.update_script_config(script['id'], config)
            
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.db.get_statistics()
        
    def refresh_scripts(self):
        """刷新脚本列表（清理不存在的文件）"""
        return self.db.cleanup_missing_scripts()
